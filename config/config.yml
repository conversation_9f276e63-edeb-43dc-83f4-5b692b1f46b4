USER_CONFIG:
  ##################################################################
  # ZRDDS_HOST
  # Description: The host name of the zDiscovery Server
  # Example    : server1.company.com
  # Required   : Yes
  ##################################################################
  ZRDDS_HOST_NAME: ""

  ##################################################################
  # GATEWAY_PORT
  # Description: The listening port of the zDiscovery Server
  # Example    : 8085
  # Required   : Yes
  ##################################################################
  GATEWAY_PORT: "8085"

  ##################################################################
  # JCL_BATCH_SIZE
  # Description: Number of JCL to send as single batch to the
  #              zDiscovery Server
  # Example    : 500
  # Required   : Yes
  ##################################################################
  JCL_BATCH_SIZE: 500

  ##################################################################
  # IDML_FILE_BATCH
  # Description: Batch size for IDML file
  # Example    : server1.company.com
  # Required   : Yes
  ##################################################################
  IDML_FILE_BATCH: 1024

  ##################################################################
  # EXPIRED_INTERVAL
  # Description: After EXPIRED_INTERVAL days that a CI haven't
  #              been discovered by the z/OS Discovery Library
  #              Adaptor, then CI will be considered as expired.
  # Example    : 30
  # Required   : Yes
  ##################################################################
  EXPIRED_INTERVAL: 30

  ##################################################################
  # PASSWORD
  # Description: The password to communicate with the zDiscovery
  #              Server.
  #              Please refer to IBM Documentation for details.
  # Required   : Yes
  ##################################################################
  PASSWORD: ""

  ##################################################################
  # KEYCLOAK_CLIENT_ID
  # Description: The CLIENT ID.
  # WARNING    : Do not change the value of this property unless
  #              instructed by IBM Support.
  # Required   : Yes
  ##################################################################
  KEYCLOAK_CLIENT_ID: "zoa-client"

  ##################################################################
  # KEYCLOAK_CLIENT_USERNAME
  # Description: The Client UserName.
  #              Please refer to IBM Documentation for details.
  # Example    : piuser
  # Required   : Yes
  ##################################################################
  KEYCLOAK_CLIENT_USERNAME: "piuser"

  ##################################################################
  # KEYCLOAK_CLIENT_PASSWORD
  # Description: The Client Password.
  #              * This value must be the base64-encoded value of
  #                the actual password set in Keycloak for the
  #                specified user ID
  #              Please refer to IBM Documentation for details.
  # Required   : Yes
  ##################################################################
  KEYCLOAK_CLIENT_PASSWORD: "Y2hhbmdlbWU="

  ##################################################################
  # GETJOBNAME_LINE_PREFIX
  # Description: Prefix to be added to each line in the getJobname output
  # Example    : ZDS_JOB
  # Required   : No
  ##################################################################
  GETJOBNAME_LINE_PREFIX: ""

  ##################################################################
  # JOB_LINE_CHAR_LIMIT
  # Description: Defines the maximum number of characters allowed per line 
  #              when displaying job names in the output. Minimum is 5.
  # Example    : 100
  # Required   : No
  ##################################################################
  JOB_LINE_CHAR_LIMIT: 100

  ##################################################################
  # ENABLE_COPY_INTO_ORIENT
  # Description: Enables or disables the execution of the `CopyIntoOrientDB` 
  #              process after every `populateDLA` or `populateDeltaDLA` execution.
  #              - Yes, YES, yes: Automatically trigger the copy process.
  #              - No, NO, no  : Do not trigger the copy process.
  # Example    : Yes, No
  # Required   : No
  ##################################################################
  ENABLE_COPY_INTO_ORIENT: "Yes"

  ##################################################################
  # COPY_INTO_ORIENT_MODE
  # Description: Defines the behavior of the `CopyIntoOrientDB` process.
  #              - Yes, YES, yes: Create a "new" OrientDB instance. 
  #                                If one exists, it will be dropped and recreated.
  #              - No, NO, no  : Perform an "update" to the existing OrientDB 
  #                                instance without recreating it.
  # Notes       : This value is used when `EnableCopyIntoOrient` is set to Yes.
  # Example    : Yes, No
  # Required   : No
  ##################################################################
  COPY_INTO_ORIENT_MODE: "Yes"

  ##################################################################
  # POPULATE_FULL_DLA
  # Description: Yes, YES, yes:
  #                   all the CIs discovered by the z/OS Discovery
  #                   Library Adaptor will be send to ServerNow.
  #              Other Value:
  #                   for the CIs discovered by the z/OS Discovery
  #                   Library Adaptor, only the changed CIs
  #                   will be send to ServiceNow.
  # Example    : Yes, YES, yes, No
  # Required   : No
  ##################################################################
  POPULATE_FULL_DLA: "No"