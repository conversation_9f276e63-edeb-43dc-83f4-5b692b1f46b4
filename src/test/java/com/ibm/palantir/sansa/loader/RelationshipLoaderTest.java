/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2026
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.loader;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import static org.mockito.Mockito.mock;

import com.ibm.palantir.catelyn.jaxb.Contains;
import com.ibm.palantir.catelyn.jaxb.RunsOn;
import com.ibm.palantir.catelyn.jaxb.Uses;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipRepository;

public class RelationshipLoaderTest {

    private RunsOn runsOn = new RunsOn();
    private Uses uses = new Uses();
    private Contains contains = new Contains();

    private RelationshipRepository relationshipRepository = mock(RelationshipRepository.class);
    private RelationshipLoader relationshipLoader = new RelationshipLoader(relationshipRepository);

    @Test
    public void testLoadRelationshipList() {
        runsOn.setSource("PRCIR1T2-PCOS-CICSRegion");
        runsOn.setTarget("17-ESC4-LPAR");
        uses.setSource("DA1D-SYS-DB2Subsystem");
        uses.setTarget("DA1GRP-LPAR400J-DB2DataSharingGroup");
        contains.setSource("DA1D-SYS-DB2Subsystem");
        contains.setTarget("DSNDB06-DA1D-SYS-Db2Database");

        List<Object> elementList = new ArrayList<>();
        elementList.add(runsOn);
        elementList.add(uses);
        elementList.add(contains);
        elementList.add(runsOn);
        elementList.add(uses);
        elementList.add(contains);

        List<String> filterIds = new ArrayList<>();

        relationshipLoader.loadRelationshipList(elementList, filterIds, "53231323", "43231323",
                "");
    }

}
