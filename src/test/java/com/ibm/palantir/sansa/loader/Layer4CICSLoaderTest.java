/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2026
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.loader;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;
import static org.powermock.api.mockito.PowerMockito.whenNew;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import com.ibm.palantir.catelyn.jaxb.SysZOSCICSFile;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSProgram;
import com.ibm.palantir.catelyn.jaxb.SysZOSCICSTransaction;
import com.ibm.palantir.catelyn.jaxb.SysZOSDB2Conn;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSDB2Conn;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSFile;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSProgram;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSRegion;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSTransaction;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.DB2Subsystem;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSDB2ConnRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSFileRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSRegionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2SubsystemRepository;

@RunWith(PowerMockRunner.class)
@PrepareForTest({})
public class Layer4CICSLoaderTest {

    private CICSProgramRepository cicsProgramRepository = mock(CICSProgramRepository.class);
    private CICSTransactionRepository cicsTransactionRepository = mock(CICSTransactionRepository.class);
    private CICSFileRepository cicsFileRepository = mock(CICSFileRepository.class);
    private CICSDB2ConnRepository cicsdb2ConnRepository = mock(CICSDB2ConnRepository.class);
    private CICSRegionRepository cicsRegionRepository = mock(CICSRegionRepository.class);
    private DB2SubsystemRepository db2SubsystemRepository = mock(DB2SubsystemRepository.class);
    private CICSRegion cicsRegion = mock(CICSRegion.class);

    private Layer4CICSLoader layer4CICSLoader = new Layer4CICSLoader(cicsProgramRepository, cicsTransactionRepository,
            cicsFileRepository, cicsdb2ConnRepository,
            cicsRegionRepository, db2SubsystemRepository);

    @Test
    public void testLoadCICSProgram() throws Exception {
        List<Object> objectList = new ArrayList<>();
        SysZOSCICSProgram sysZOSCICSProgram = mock(SysZOSCICSProgram.class);
        objectList.add(sysZOSCICSProgram);
        when(sysZOSCICSProgram.getName()).thenReturn("abc");
        when(sysZOSCICSProgram.getId()).thenReturn("123");
        CICSProgram cicsProgram = mock(CICSProgram.class);
        whenNew(CICSProgram.class).withNoArguments().thenReturn(cicsProgram);
        doReturn(cicsProgram).when(cicsProgramRepository).save(cicsProgram);

        layer4CICSLoader.loadCICSProgramList(objectList, "53231323", "");
    }

    @Test
    public void testLoadCICSTransaction() throws Exception {
        List<Object> objectList = new ArrayList<>();
        SysZOSCICSTransaction sysZOSCICSTransaction = mock(SysZOSCICSTransaction.class);
        objectList.add(sysZOSCICSTransaction);
        when(sysZOSCICSTransaction.getName()).thenReturn("abc");
        when(sysZOSCICSTransaction.getId()).thenReturn("123");
        when(sysZOSCICSTransaction.getInitialProgram()).thenReturn("programA");
        CICSTransaction cicsTransaction = mock(CICSTransaction.class);
        whenNew(CICSTransaction.class).withNoArguments().thenReturn(cicsTransaction);
        doReturn(cicsTransaction).when(cicsTransactionRepository).save(cicsTransaction);

        layer4CICSLoader.loadCICSTransactionList(objectList, "53231323", "", new ArrayList<>(),"","");
    }

    @Test
    public void testLoadCICSFile() throws Exception {
        List<Object> objectList = new ArrayList<>();
        SysZOSCICSFile sysZOSCICSFile = mock(SysZOSCICSFile.class);
        objectList.add(sysZOSCICSFile);
        when(sysZOSCICSFile.getDDName()).thenReturn("abc");
        when(sysZOSCICSFile.getId()).thenReturn("123");
        CICSFile cicsFile = mock(CICSFile.class);
        whenNew(CICSFile.class).withNoArguments().thenReturn(cicsFile);
        doReturn(cicsFile).when(cicsFileRepository).save(cicsFile);

        layer4CICSLoader.loadCICSFileList(objectList, "53231323", "");
    }

    @Test
    public void testLoadCICSDB2Conn() throws Exception {
        SysZOSDB2Conn sysZOSDB2Conn = mock(SysZOSDB2Conn.class);
        when(sysZOSDB2Conn.getId()).thenReturn("CICSCWA1-DC1V-SYS-DB2Conn");
        when(sysZOSDB2Conn.getSMFID()).thenReturn("SYS");
        when(sysZOSDB2Conn.getDB2Id()).thenReturn("DC1V");
        when(sysZOSDB2Conn.getCICSRegionJN()).thenReturn("CICSCWA1");
        when(sysZOSDB2Conn.getCICSProgramName()).thenReturn("abc");

        CICSDB2Conn cicsdb2Conn = mock(CICSDB2Conn.class);
        whenNew(CICSDB2Conn.class).withNoArguments().thenReturn(cicsdb2Conn);
        doReturn(cicsdb2Conn).when(cicsdb2ConnRepository).save(cicsdb2Conn);

        DB2Subsystem db2Subsystem = mock(DB2Subsystem.class);
        whenNew(DB2Subsystem.class).withNoArguments().thenReturn(db2Subsystem);
        doReturn(db2Subsystem).when(db2SubsystemRepository).save(db2Subsystem);

        CICSRegion cicsRegion = mock(CICSRegion.class);
        whenNew(CICSRegion.class).withNoArguments().thenReturn(cicsRegion);
        doReturn(cicsRegion).when(cicsRegionRepository).save(cicsRegion);

        CICSProgram cicsProgram = mock(CICSProgram.class);
        whenNew(CICSProgram.class).withNoArguments().thenReturn(cicsProgram);
        doReturn(cicsProgram).when(cicsProgramRepository).save(cicsProgram);

        layer4CICSLoader.loadCICSDB2Conn(sysZOSDB2Conn, "53231323", "");
    }
}