/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.sansa.loader;

import org.junit.Test;
import org.junit.runner.RunWith;
import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;
import static org.powermock.api.mockito.PowerMockito.whenNew;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSFile;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSProgram;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSRegion;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTable;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTableOverride;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSTransaction;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSFileRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableOverrideRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipRepository;

@RunWith(PowerMockRunner.class)
@PrepareForTest({})
public class ZReportFileLoaderTest {

    private CICSSystemInitTableRepository cicsSystemInitTableRepository = mock(CICSSystemInitTableRepository.class);
    private CICSSystemInitTableOverrideRepository cicsSystemInitTableOverrideRepository = mock(
            CICSSystemInitTableOverrideRepository.class);
    private CICSProgramRepository cicsProgramRepository = mock(CICSProgramRepository.class);
    private CICSTransactionRepository cicsTransactionRepository = mock(CICSTransactionRepository.class);
    private CICSFileRepository cicsFileRepository = mock(CICSFileRepository.class);
    private RelationshipRepository relationshipRepository = mock(RelationshipRepository.class);
    private CICSRegion cicsRegion = mock(CICSRegion.class);

    private ZReportFileLoader zReportFileLoader = new ZReportFileLoader(cicsSystemInitTableRepository,
            cicsSystemInitTableOverrideRepository, cicsProgramRepository,
            cicsTransactionRepository, cicsFileRepository, relationshipRepository);

    @Test
    public void testLoadCICSSit() throws Exception {
        CICSSystemInitTable cicsSystemInitTable = mock(CICSSystemInitTable.class);
        whenNew(CICSSystemInitTable.class).withNoArguments().thenReturn(cicsSystemInitTable);
        doReturn(cicsSystemInitTable).when(cicsSystemInitTableRepository).save(cicsSystemInitTable);

        zReportFileLoader.loadCICSSit("123", "abc", "", "53231323", "");
    }

    @Test
    public void testLoadCICSSitOverrides() throws Exception {
        CICSSystemInitTableOverride cicsSystemInitTableOverride = mock(CICSSystemInitTableOverride.class);
        whenNew(CICSSystemInitTableOverride.class).withNoArguments().thenReturn(cicsSystemInitTableOverride);
        doReturn(cicsSystemInitTableOverride).when(cicsSystemInitTableOverrideRepository)
                .save(cicsSystemInitTableOverride);

        zReportFileLoader.loadCICSSitOverrides("123", "abc", "", "53231323", "");
    }

    @Test
    public void testLoadPrograms() throws Exception {
        CICSProgram cicsProgram = mock(CICSProgram.class);
        when(cicsRegion.getJobName()).thenReturn("jn");
        whenNew(CICSProgram.class).withNoArguments().thenReturn(cicsProgram);
        String content = "" +
                "Program  Module   Language Language Install  CEDF     Program  Data     Executn  Executn  Reload   Load     Hold\n"
                +
                "Name     Type     Defined  Deduced  Type     Allowed? Status   Location Key      Set      Status   Status   Status\n"
                +
                "\n" +
                "CEECSTX  Program  NtDefind NtDeducd SysAuto  Yes      Enabled  Below    User     Fullapi  No       NtLoadbl Task\n"
                +
                "CJH9NXLM Program  Assemblr Assemblr SysAuto  No       Enabled  Any      CICS     Fullapi  No       Loadable Task";

        when(cicsRegion.getDlaId()).thenReturn("jn-123-CICSRegion");
        doReturn(cicsProgram).when(cicsProgramRepository).save(cicsProgram);

        zReportFileLoader.loadPrograms(content, cicsRegion, "123", "");

    }

    @Test
    public void testLoadTransactions() throws Exception {
        CICSTransaction cicsTransaction = mock(CICSTransaction.class);
        CICSProgram cicsProgram = mock(CICSProgram.class);
        when(cicsRegion.getJobName()).thenReturn("jn");
        whenNew(CICSTransaction.class).withNoArguments().thenReturn(cicsTransaction);
        String content = "Tran Initial  Tran Data     Data\n" +
                "ID   Program  Dump Location Key\n" +
                "\n" +
                "CADP DFHDPLU  Yes  Any      CICS\n" +
                "CATD DFHZATD  Yes  Any      CICS";

        when(cicsRegion.getDlaId()).thenReturn("jn-123-CICSRegion");
        doReturn(cicsTransaction).when(cicsTransactionRepository).save(cicsTransaction);

        zReportFileLoader.loadTransactions(content, cicsRegion, "123", "");
    }

    @Test
    public void testLoadCICSFiles() throws Exception {
        CICSFile cicsFile = mock(CICSFile.class);
        when(cicsRegion.getJobName()).thenReturn("jn");
        whenNew(CICSFile.class).withNoArguments().thenReturn(cicsFile);

        String content = "DD Name  Dataset Name\n" +
                "\n" +
                "DFHCMACD\n" +
                "DFHCSD   CICS54.CICSCWA1.DFHCSD";
        when(cicsRegion.getDlaId()).thenReturn("jn-123-CICSRegion");
        doReturn(cicsFile).when(cicsFileRepository).save(cicsFile);

        zReportFileLoader.loadCICSFiles(content, cicsRegion, "123", "");
    }
}