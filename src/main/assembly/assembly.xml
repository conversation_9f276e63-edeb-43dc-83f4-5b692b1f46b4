<!-- ************************************************************************** -->
<!-- Licensed Materials - Property of IBM                                       -->
<!-- 5698-Z<PERSON> (C) Copyright IBM Corp. 2021-2026                                 -->
<!-- All Rights Reserved                                                        -->
<!-- US Government Users Restricted Rights - Use, duplication or                -->
<!-- disclosure restricted by GSA ADP Schedule Contract with IBM Corp.          -->
<!-- ************************************************************************** -->

<assembly>
	<id>standalone</id>
	<formats>
		<format>tar</format>
	</formats>
	<fileSets>
		<fileSet>
			<directory>config</directory>
			<outputDirectory>/config</outputDirectory>
			<includes>
				<include>config.yml</include>
				<include>file_dynamic_jcl.yml</include>
				<include>file_idml.yml</include>
				<include>file_routecard.yml</include>
				<include>file_static_jcl.yml</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>internal_config</directory>
			<outputDirectory>/internal_config</outputDirectory>
			<includes>
				<include>agent.yml</include>
				<include>log.xml</include>
			</includes>
		</fileSet>
		<fileSet>
			<directory>script</directory>
			<outputDirectory>/script</outputDirectory>
			<includes>
				<include>detectExpiredDLAItems.sh</include>
				<include>getCICount.sh</include>
				<include>getJobName.sh</include>
				<include>resendExpiredDLAItems.sh</include>
			</includes>
			<fileMode>0755</fileMode>
		</fileSet>
		<fileSet>
			<outputDirectory>log</outputDirectory>
			<excludes>
				<exclude>**/*</exclude>
			</excludes>
		</fileSet>
	</fileSets>
	<files>
		<file>
			<source>lib/discovery_agent_utils_${project.version}.jar</source>
			<outputDirectory>/lib</outputDirectory>
			<destName>discovery_agent_utils.jar</destName>
		</file>
		<file>
			<source>target/${artifactId}-${version}.${packaging}</source>
			<outputDirectory>/lib</outputDirectory>
			<destName>discovery_agent.jar</destName>
			<fileMode>0644</fileMode>
		</file>
		<file>
			<source>script/discovery_agent.sh</source>
			<outputDirectory>/script</outputDirectory>
			<fileMode>0755</fileMode>
		</file>

		<file>
			<source>target/lib/gson-2.13.2.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/json-20250107.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/snakeyaml-2.5.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/logback-classic-1.5.20.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/logback-core-1.5.20.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/slf4j-api-2.0.17.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/kafka-clients-4.1.0.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/kafka-streams-4.1.0.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/httpclient-4.5.14.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/httpmime-4.5.14.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/httpcore-4.4.16.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/commons-logging-1.3.0.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/commons-codec-1.19.0.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/jackson-core-2.18.2.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/jackson-databind-2.18.2.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/jackson-annotations-2.18.2.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/com.ibm.systemz.jcl.editor.core-1.2.38-SNAPSHOT.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
	</files>
</assembly>