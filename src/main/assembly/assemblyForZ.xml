<!-- ************************************************************************** -->
<!-- Licensed Materials - Property of IBM                                       -->
<!-- 5698-Z<PERSON> (C) Copyright IBM Corp. 2021-2026                                 -->
<!-- All Rights Reserved                                                        -->
<!-- US Government Users Restricted Rights - Use, duplication or                -->
<!-- disclosure restricted by GSA ADP Schedule Contract with IBM Corp.          -->
<!-- ************************************************************************** -->

<assembly>
	<id>mainframe</id>
	<formats>
		<format>tar</format>
	</formats>
	<fileSets>
		<fileSet>
			<directory>ZJOB</directory>
			<outputDirectory>/ZJOB</outputDirectory>
		</fileSet>
		<fileSet>
			<outputDirectory>log</outputDirectory>
			<excludes>
				<exclude>**/*</exclude>
			</excludes>
		</fileSet>
	</fileSets>
	<files>
		<file>
			<source>target/${artifactId}-${version}.${packaging}</source>
			<outputDirectory>/lib</outputDirectory>
			<destName>discovery_agent.jar</destName>
			<fileMode>0644</fileMode>
		</file>
		<file>
			<source>script/discovery_agent_on_z.sh</source>
			<outputDirectory>/script</outputDirectory>
			<destName>discovery_agent.sh</destName>
			<fileMode>0755</fileMode>
		</file>
		<file>
			<source>script/getJobName_on_z.sh</source>
			<outputDirectory>/script</outputDirectory>
			<destName>getJobName.sh</destName>
			<fileMode>0755</fileMode>
		</file>
		<file>
			<source>script/getCICount_on_z.sh</source>
			<outputDirectory>/script</outputDirectory>
			<destName>getCICount.sh</destName>
			<fileMode>0755</fileMode>
		</file>
		<file>
			<source>script/detectExpiredDLAItems_on_z.sh</source>
			<outputDirectory>/script</outputDirectory>
			<destName>detectExpiredDLAItems.sh</destName>
			<fileMode>0755</fileMode>
		</file>
		<file>
			<source>script/resendExpiredDLAItems_on_z.sh</source>
			<outputDirectory>/script</outputDirectory>
			<destName>resendExpiredDLAItems.sh</destName>
			<fileMode>0755</fileMode>
		</file>
		<file>
			<source>lib/discovery_agent_utils_${project.version}.jar</source>
			<outputDirectory>/lib</outputDirectory>
			<destName>discovery_agent_utils_${project.version}.jar</destName>
			<fileMode>0644</fileMode>
		</file>
		<file>
			<source>internal_config/agent.yml</source>
			<outputDirectory>/internal_config</outputDirectory>
			<destName>agent.yml</destName>
		</file>
		<file>
			<source>internal_config/log.xml</source>
			<outputDirectory>/internal_config</outputDirectory>
			<destName>log.xml</destName>
		</file>
		<file>
			<source>config/config.yml</source>
			<outputDirectory>/config</outputDirectory>
			<destName>CONFIG.YML</destName>
		</file>
		<file>
			<source>config/file_dynamic_jcl.yml</source>
			<outputDirectory>/config</outputDirectory>
			<destName>FILE_DYNAMIC_JCL.YML</destName>
		</file>
		<file>
			<source>config/file_idml.yml</source>
			<outputDirectory>/config</outputDirectory>
			<destName>FILE_IDML.YML</destName>
		</file>
		<file>
			<source>config/file_routecard.yml</source>
			<outputDirectory>/config</outputDirectory>
			<destName>FILE_ROUTECARD.YML</destName>
		</file>
		<file>
			<source>config/file_static_jcl.yml</source>
			<outputDirectory>/config</outputDirectory>
			<destName>FILE_STATIC_JCL.YML</destName>
		</file>
		<file>
			<source>target/lib/gson-2.13.2.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/json-20250107.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/snakeyaml-2.5.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/logback-classic-1.5.20.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/logback-core-1.5.20.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/slf4j-api-2.0.17.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/kafka-clients-4.1.0.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/kafka-streams-4.1.0.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/httpclient-4.5.14.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/httpmime-4.5.14.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/httpcore-4.4.16.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/commons-logging-1.3.0.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/commons-codec-1.19.0.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/jackson-core-2.18.2.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/jackson-databind-2.18.2.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/jackson-annotations-2.18.2.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
		<file>
			<source>target/lib/com.ibm.systemz.jcl.editor.core-1.2.38-SNAPSHOT.jar</source>
			<outputDirectory>/lib</outputDirectory>
		</file>
	</files>
</assembly>