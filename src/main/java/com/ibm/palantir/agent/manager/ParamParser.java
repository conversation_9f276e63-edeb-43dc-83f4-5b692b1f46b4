/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.agent.manager;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.yaml.snakeyaml.LoaderOptions;
import org.yaml.snakeyaml.Yaml;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.ibm.palantir.agent.AgentStandalone;
import com.ibm.palantir.agent.logger.LoggerUtils;
import com.ibm.palantir.agent.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.agent.manager.pipeline.PipelineMode;
import com.ibm.palantir.catelyn.config.entity.ExtendItem;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.util.GsonUtils;

public class ParamParser {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = ParamParser.class.getSimpleName();
    private static final int EXPECTED_ARGS_LENGTH = 2;
    private static final String AGENT_FILE_PATH = "internal_config/agent.yml";

    private HashMap<String, String> typeMap = new HashMap<String, String>() {
        {
            put(AgentStandalone.ROUTE_CARD, "ROUTECARD_FILE");
            put(AgentStandalone.STATIC, "JOB_FILE");
            put(AgentStandalone.DYNAMIC, "DYNAMIC_FILE");
            put(AgentStandalone.IDML, "IDMLBOOK_FILE");
        }
    };

    private List<String> persistTypeList = new ArrayList<>(
            Arrays.asList("API_JCL", "API_DLA", "File", "JDBC", "Kafka", "API_UTILS"));

    public Set<String> getTypeSet() {
        return this.typeMap.keySet();
    }

    public Map<String, JsonObject> paramParser(String[] args, PipelineMode pipelineMode)
            throws ServiceException, JsonProcessingException {
        LOG.log(LogLevel.INFO, CLASSNAME, "paramParser", "Running Agent with the following parameters: {}",
                String.join(" ", args));
        validateArguments(args);

        JsonObject userConfig = processUserConfiguration(args);
        String fileParam = args[1];
        setProperties(userConfig);
        // Set the config in the pipelineMode object.
        setPersistConfig(userConfig, pipelineMode);
        /* Process the file parameter */
        JsonObject fileParamJson = getJsonFromYAML(fileParam);
        LOG.log(LogLevel.INFO, CLASSNAME, "paramParser", "Param parsing got completed...");
        return getFileJsonObject(fileParamJson);
    }

    private void validateArguments(String[] args) throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "validateArguments", "Running validateArguments");
        if (args == null || args.length != EXPECTED_ARGS_LENGTH) {
            LOG.log(LogLevel.ERROR, CLASSNAME, "validateArguments",
                    "Invalid arguments. Requires two parameters: <user_config YML> <file YML>\n" +
                            "<user_config YML> - Configuration file in YAML to communicate with IBM Z Discovery Server.\n"
                            +
                            "<file YML> - Configuration file in YAML containing a list of files to be processed.");
            throw new IllegalArgumentException("Invalid input parameters. Expected exactly two arguments.", null);
        }
    }

    private JsonObject processUserConfiguration(String[] args)
            throws ServiceException, JsonProcessingException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "processUserConfiguration", "Running processUserConfiguration");
        if (args == null || args.length == 0) {
            throwServiceException("No arguments found. At-least user-config is expected");
        }

        JsonObject userConfig = null;
        JsonObject agentConfig = getJsonFromYAML(AGENT_FILE_PATH);

        if (args.length == 1 || args.length == 2) {
            String userConfigParam = args[0];
            if (userConfigParam == null || userConfigParam.isEmpty()) {
                throwServiceException(
                        "Invalid argument. A non-empty user-config file path is expected as the first argument.");
            }
            userConfig = validateUserConfigFile(userConfigParam);
        } else {
            throwServiceException("Invalid number of arguments. Expected one or two arguments.");
        }

        if (userConfig == null || userConfig.isEmpty() && agentConfig == null || agentConfig.isEmpty()) {
            throwServiceException("No arguments found. At-least user-config is expected");
        }

        return mergeUserConfigWithAgentConfig(userConfig, agentConfig);
    }

    private JsonObject validateUserConfigFile(String userConfigParam) throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "validateUserConfigFile", "Running validateUserConfigFile");
        JsonObject userConfig = null;

        try {
            userConfig = getJsonFromYAML(userConfigParam);
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(userConfig.toString());

            // Check if the file contains the "USER_CONFIG" object
            if (rootNode == null || !rootNode.has("USER_CONFIG")) {
                throwServiceException(
                        "The provided file does not contain 'USER_CONFIG' and cannot be used as a USER-CONFIG file.");
            }
        } catch (Exception e) {
            throwServiceException(
                    "Error reading or parsing the file. Ensure the file is a valid USER-CONFIG YAML file. " + e);
        }
        return userConfig;
    }

    private JsonObject mergeUserConfigWithAgentConfig(JsonObject userConfig, JsonObject agentConfig)
            throws JsonProcessingException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "mergeUserConfigWithAgentConfig", "Running mergeUserConfigWithAgentConfig");
        ObjectMapper objectMapper = new ObjectMapper();

        JsonNode userConfigNode = objectMapper.readTree(userConfig.toString()).get("USER_CONFIG");
        JsonNode agentConfigNode = objectMapper.readTree(agentConfig.toString());
        // Apply the populateDLAUrl logic first
        agentConfigNode = applyUrlLogic(agentConfigNode, userConfigNode);

        // Perform placeholder replacements
        JsonNode updatedAgentNode = replacePlaceholders(agentConfigNode, userConfigNode);

        String updatedJsonString = objectMapper.writeValueAsString(updatedAgentNode);

        return JsonParser.parseString(updatedJsonString).getAsJsonObject();
    }

    private JsonNode applyUrlLogic(JsonNode agentConfigNode, JsonNode userConfigNode) {
        if (agentConfigNode.isObject()) {
            ObjectNode objectNode = (ObjectNode) agentConfigNode;
            agentConfigNode.fields().forEachRemaining(entry -> {
                if (entry.getKey().equals("persist") && entry.getValue().isArray()) {
                    ArrayNode persistArray = (ArrayNode) entry.getValue();
                    for (JsonNode persistObject : persistArray) {
                        if (persistObject.isObject() && "API_DLA".equals(persistObject.get("type").asText())) {
                            ObjectNode persistObjectNode = (ObjectNode) persistObject;
                            ObjectNode optionsNode = (ObjectNode) persistObjectNode.get("option");
                            if (optionsNode != null) {
                                applyPopulateDLAUrlLogic(userConfigNode, optionsNode);
                                applyCopyIntoOrientUrlLogic(userConfigNode, optionsNode);
                                persistObjectNode.set("option", optionsNode);
                            }
                        }
                    }
                }
                objectNode.set(entry.getKey(), applyUrlLogic(entry.getValue(), userConfigNode));
            });
            return objectNode;
        } else if (agentConfigNode.isArray()) {
            ArrayNode arrayNode = (ArrayNode) agentConfigNode;
            for (int i = 0; i < arrayNode.size(); i++) {
                arrayNode.set(i, applyUrlLogic(arrayNode.get(i), userConfigNode));
            }
            return arrayNode;
        }
        return agentConfigNode;
    }

    private ObjectNode applyPopulateDLAUrlLogic(JsonNode userConfigNode, ObjectNode optionsNode) {
        // Fetch value of POPULATE_FULL_DLA
        String populateFullDLAValue = userConfigNode.has("POPULATE_FULL_DLA")
                ? userConfigNode.get("POPULATE_FULL_DLA").asText()
                : "";

        if (optionsNode != null) {
            if (populateFullDLAValue.equalsIgnoreCase("YES")) {
                optionsNode.remove("populateDeltaDLAUrl"); // Remove populateDeltaDLAUrl
            } else {
                optionsNode.remove("populateDLAUrl"); // Remove populateDLAUrl
            }
        }
        return optionsNode;
    }

    private ObjectNode applyCopyIntoOrientUrlLogic(JsonNode userConfigNode, ObjectNode optionsNode) {
        // Fetch value of ENABLE_COPY_INTO_ORIENT
        String enableCopyIntoOrientValue = userConfigNode.has("ENABLE_COPY_INTO_ORIENT")
                ? userConfigNode.get("ENABLE_COPY_INTO_ORIENT").asText().trim()
                : "";

        if (optionsNode != null) {
            if (enableCopyIntoOrientValue.equalsIgnoreCase("YES") || enableCopyIntoOrientValue.equalsIgnoreCase("NO")) {
                if (enableCopyIntoOrientValue.equalsIgnoreCase("NO")) {
                    optionsNode.remove("copyIntoOrientUrl"); // Remove copyIntoOrientUrl
                }
            } else {
                optionsNode.remove("copyIntoOrientUrl");
                LOG.log(LogLevel.ERROR, CLASSNAME, "applyCopyIntoOrientUrlLogic", "Invalid value for ENABLE_COPY_INTO_ORIENT. Please provide a valid configuration value.");
            }
        }
        return optionsNode;
    }

    private JsonNode replacePlaceholders(JsonNode agentConfigNode, JsonNode userConfigNode) {
        if (agentConfigNode.isObject()) {
            ObjectNode objectNode = (ObjectNode) agentConfigNode;
            agentConfigNode.fields().forEachRemaining(
                    entry -> objectNode.set(entry.getKey(), replacePlaceholders(entry.getValue(), userConfigNode)));
            return objectNode;
        } else if (agentConfigNode.isArray()) {
            ArrayNode arrayNode = (ArrayNode) agentConfigNode;
            for (int i = 0; i < arrayNode.size(); i++) {
                arrayNode.set(i, replacePlaceholders(arrayNode.get(i), userConfigNode));
            }
            return arrayNode;
        } else if (agentConfigNode.isTextual()) {
            String text = agentConfigNode.asText();
            text = replaceAllPlaceholders(text, userConfigNode);
            return JsonNodeFactory.instance.textNode(text);
        }
        return agentConfigNode;
    }

    private String replaceAllPlaceholders(String text, JsonNode userConfigNode) {
        String pattern = "\\$\\{USER_CONFIG\\.([a-zA-Z0-9_]+)}";
        java.util.regex.Matcher matcher = java.util.regex.Pattern.compile(pattern).matcher(text);
        while (matcher.find()) {
            String key = matcher.group(1);
            JsonNode valueNode = userConfigNode.get(key);
            if (valueNode != null) {
                // Replace placeholder with the actual value
                text = text.replace("${USER_CONFIG." + key + "}", valueNode.asText());
            }
        }
        return text;
    }

    private JsonObject getJsonFromYAML(String path) throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "getJsonFromYAML", "Running getJsonFromYAML with params - path: {}", path);
        FileInputStream file = null;
        try {
            LoaderOptions loadOptions = new LoaderOptions();
            loadOptions.setCodePointLimit(100 * 1024 * 1024);
            file = new FileInputStream(path);
            Yaml yaml = new Yaml(loadOptions);
            return GsonUtils.toJsonElementWithNull(yaml.load(file)).getAsJsonObject();
        } catch (FileNotFoundException e) {
            String errorCode = ErrorCode.FileNotExist.getCodeStr();
            String errorMsg = MessageFormat.format(MsgTemp.get(errorCode),
                    path);
            LOG.log(LogLevel.DEBUG, CLASSNAME, "getJsonFromYAML", "Error: {}", errorMsg);
            throw new ServiceException(errorCode, errorMsg, CLASSNAME, "getJsonFromYAML");
        } catch (Exception e) {
            String paramFormatErrorCode = ErrorCode.GeneralError.getCodeStr();
            String paramFormatErrorMsg = MessageFormat.format(MsgTemp.get(paramFormatErrorCode),
                    MessageFormat.format("Read YAML file {0} throw error: {1}", path, e.getMessage()));
            LOG.log(LogLevel.DEBUG, CLASSNAME, "getJsonFromYAML", "Error: {}", paramFormatErrorMsg);
            throw new ServiceException(paramFormatErrorCode, paramFormatErrorMsg, CLASSNAME, "getJsonFromYAML");
        } finally {
            try {
                if (file != null) {
                    file.close();
                    file = null;
                }
            } catch (IOException e) {
                String FileIOErrorCode = ErrorCode.FileIOError.getCodeStr();
                String paramFormatErrorMsg = MessageFormat.format(MsgTemp.get(FileIOErrorCode),
                        path, "file close error: " + e.getLocalizedMessage());
                LOG.log(LogLevel.DEBUG, CLASSNAME, "getJsonFromYAML", "Error: {}", paramFormatErrorMsg);
                throw new ServiceException(FileIOErrorCode, paramFormatErrorMsg, CLASSNAME, "getJsonFromYAML");
            }
        }
    }

    private void setProperties(JsonObject configJson) throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "setProperties", "Running setProperties");
        try {
            AgentProperties agentProperties = AgentProperties.getAgentProperties();
            if (configJson.has("config") && configJson.get("config").isJsonObject()) {
                JsonObject propertiesJson = configJson.getAsJsonObject("config");
                if (propertiesJson.has("jcl_batch_size")) {
                    agentProperties.setJCL_BATCH_SIZE(propertiesJson.get("jcl_batch_size").getAsInt());
                }
                if (propertiesJson.has("idml_file_batch")) {
                    agentProperties.setIDML_FILE_BATCH(propertiesJson.get("idml_file_batch").getAsInt());
                }
                if (propertiesJson.has("copy_into_orient_mode")) {
                    String copyIntoOrientMode = propertiesJson.get("copy_into_orient_mode").getAsString().trim();
                    if (copyIntoOrientMode.equalsIgnoreCase("YES") || copyIntoOrientMode.equalsIgnoreCase("NO")) {
                        agentProperties.setCopyIntoOrientMode(copyIntoOrientMode);
                    } else {
                        throwServiceException(
                                "Invalid value for COPY_INTO_ORIENT_MODE. Please provide a valid configuration value.");
                    }
                }
            }
        } catch (Exception e) {
            String paramFormatErrorCode = ErrorCode.GeneralError.getCodeStr();
            String paramFormatErrorMsg = MessageFormat.format(MsgTemp.get(paramFormatErrorCode),
                    MessageFormat.format("Set properties throw error: {0}", e.getMessage()));
            LOG.log(LogLevel.DEBUG, CLASSNAME, "setProperties", "Error: {}", paramFormatErrorMsg);
            throw new ServiceException(paramFormatErrorCode, paramFormatErrorMsg, CLASSNAME, "getJsonFromYAML");
        }

    }

    private void setPersistConfig(JsonObject persistJson, PipelineMode pipelineMode) throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "setPersistConfig", "Running setPersistConfig");
        String errCode = ErrorCode.ConfigurationJsonFormatError.getCodeStr();
        if (persistJson.has("persist")) {
            if (persistJson.get("persist").isJsonNull()) {
                return;
            }
            if (!persistJson.get("persist").isJsonArray()) {
                String msg = MessageFormat.format(MsgTemp.get(errCode),
                        "persist", "The persist item list format is not JsonArray.");
                LOG.log(LogLevel.DEBUG, CLASSNAME, "setPersistConfig", "Error: {}", msg);
                throw new ServiceException(errCode, msg, CLASSNAME, "setPersistConfig");
            }
            JsonArray persistList = persistJson.getAsJsonArray("persist");
            for (JsonElement persistElement : persistList) {
                if (!persistElement.isJsonObject()) {
                    String msg = MessageFormat.format(MsgTemp.get(errCode),
                            "persist", "The persist item format is not JsonObject.");
                    LOG.log(LogLevel.DEBUG, CLASSNAME, "setPersistConfig", "Error: {}", msg);
                    throw new ServiceException(errCode, msg, CLASSNAME, "setPersistConfig");
                }
                JsonObject persistItem = persistElement.getAsJsonObject();
                ExtendItem extendItem = GsonUtils.fromJson(persistItem, ExtendItem.class);
                if (extendItem.getVersion() == null) {
                    extendItem.setVersion("default");
                }
                if (extendItem.verify()) {
                    String type = extendItem.getType();
                    if (this.persistTypeList.contains(type)) {
                        pipelineMode.addConfig(extendItem);
                    } else {
                        String msg = MessageFormat.format(MsgTemp.get(errCode), "persist",
                                MessageFormat.format("The persist item type is not in [{0}].",
                                        String.join(",", this.persistTypeList)));
                        throw new ServiceException(errCode, msg, CLASSNAME, "setPersistConfig");
                    }
                } else {
                    String msg = MessageFormat.format(MsgTemp.get(errCode),
                            "persist", "The persist item format is not correct.");
                    LOG.log(LogLevel.DEBUG, CLASSNAME, "setPersistConfig", "Error: {}", msg);
                    throw new ServiceException(errCode, msg, CLASSNAME, "setPersistConfig");
                }
            }
        } else {
            String msg = MessageFormat.format(MsgTemp.get(errCode),
                    "persist", "The json don't contain persist key.");
            LOG.log(LogLevel.DEBUG, CLASSNAME, "setPersistConfig", "Error: {}", msg);
            throw new ServiceException(errCode, msg, CLASSNAME, "setPersistConfig");
        }
    }

    private void throwServiceException(String errorMessage) throws ServiceException {
        String errCode = ErrorCode.ProcessError.getCodeStr();
        String msg = MessageFormat.format(MsgTemp.get(errCode), errorMessage);
        LOG.log(LogLevel.DEBUG, CLASSNAME, "throwServiceException", "Error: {}", msg);
        throw new ServiceException(errCode, msg, CLASSNAME, "throwServiceException");
    }

    private Map<String, JsonObject> getFileJsonObject(JsonObject configJson) throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "getFileJsonObject", "Running getFileJsonObject");
        if (configJson.has("files") && configJson.get("files").isJsonArray()) {
            Map<String, JsonObject> fileMap = new HashMap<>();
            JsonArray configArray = configJson.getAsJsonArray("files");
            List<JsonElement> illegalList = new ArrayList<>();
            for (JsonElement jsonElement : configArray) {
                if (jsonElement.isJsonObject()) {
                    JsonObject jsonEntity = jsonElement.getAsJsonObject();
                    if (!jsonEntity.has("type")) {
                        LOG.log(LogLevel.WARN, CLASSNAME, "getFileJsonObject", MessageFormat.format(
                                "Skipping entity: Key \"type\" is missing from the entry {0}.",
                                jsonElement.toString()));
                        illegalList.add(jsonElement);
                    } else {
                        String type = jsonEntity.get("type").getAsString();
                        Set typeList = getTypeSet();
                        if (!typeList.contains(type)) {
                            LOG.log(LogLevel.WARN, CLASSNAME, "getFileJsonObject", MessageFormat.format(
                                    "Skipping this entity: Value for key \"type\" is {0}.  It must be one of the following values: {1}.  Current input value is: {2}",
                                    type, String.join(",", typeList), jsonElement.toString()));
                            illegalList.add(jsonElement);
                        }
                        continue;
                    }
                } else {
                    LOG.log(LogLevel.WARN, CLASSNAME, "getFileJsonObject", MessageFormat.format(
                            "The YAML config entity structure is error. Skip this entity. The entity is converted to JSON: {0}",
                            jsonElement.toString()));
                    illegalList.add(jsonElement);
                }
            }
            for (JsonElement illegalElement : illegalList) {
                configArray.remove(illegalElement);
            }
            for (String type : getTypeSet()) {
                JsonObject jsonObject = createFileJsonObject(configArray, type);
                if (jsonObject != null) {
                    fileMap.put(type, jsonObject);
                }
            }
            return fileMap;
        } else {
            String errCode = ErrorCode.ConfigurationError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode),
                    "The input YML config file doesn't contains the \"files\" key, or the \"files\" key does not contains a list.");
            LOG.log(LogLevel.DEBUG, CLASSNAME, "getFileJsonObject", "Error: {}", msg);
            throw new ServiceException(errCode, msg, CLASSNAME, "getFileJsonObject");
        }
    }

    private JsonObject createFileJsonObject(JsonArray configJson, String type) throws ServiceException {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("FORMAT", this.typeMap.get(type));
        jsonObject.add("CONTENT", new JsonArray());
        for (JsonElement mapEntity : configJson) {
            JsonObject fileJson = new JsonObject();
            JsonObject entityJson = mapEntity.getAsJsonObject();
            if (entityJson.get("type").getAsString().equals(type)) {
                for (String key : entityJson.keySet()) {
                    JsonElement entityValue = entityJson.get(key);
                    if (entityValue.isJsonNull()) {
                        fileJson.addProperty(key.toUpperCase(), "");
                    } else {
                        fileJson.add(key.toUpperCase(), entityValue);
                    }
                }
                if (fileJson.has("SYSPLEX_NAME") && fileJson.has("DATASET_NAME") && fileJson.has("MEMBER_NAME")) {
                    jsonObject.getAsJsonArray("CONTENT").add(fileJson);
                } else {
                    String errCode = ErrorCode.ConfigurationError.getCodeStr();
                    String msg = MessageFormat.format(MsgTemp.get(errCode),
                            "The file config don't contain sysplex_name, dataset_name, or member_name.");
                    LOG.log(LogLevel.DEBUG, CLASSNAME, "createFileJsonObject", "Error: {}", msg);
                    throw new ServiceException(errCode, msg, CLASSNAME, "createFileJsonObject");
                }
            }
        }
        if (jsonObject.getAsJsonArray("CONTENT").size() == 0) {
            return null;
        }
        return jsonObject;
    }
}