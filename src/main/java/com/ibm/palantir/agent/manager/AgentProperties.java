package com.ibm.palantir.agent.manager;

public class AgentProperties {
    private volatile static AgentProperties agentProperties;

    private int JCL_BATCH_SIZE;

    private int IDML_FILE_BATCH;

    private String copyIntoOrientMode;

    private AgentProperties() {
        this.JCL_BATCH_SIZE = 500;
        this.IDML_FILE_BATCH = 1024;
    }

    public static AgentProperties getAgentProperties() {
        if (agentProperties == null) {
            synchronized (AgentProperties.class) {
                if (agentProperties == null) {
                    agentProperties = new AgentProperties();
                }
            }
        }
        return agentProperties;
    }

    public int getJCL_BATCH_SIZE() {
        return this.JCL_BATCH_SIZE;
    }

    public void setJCL_BATCH_SIZE(int BATCH_SIZE) {
        this.JCL_BATCH_SIZE = BATCH_SIZE;
    }

    public int getIDML_FILE_BATCH() {
        return IDML_FILE_BATCH;
    }

    public void setIDML_FILE_BATCH(int IDML_FILE_BATCH) {
        this.IDML_FILE_BATCH = IDML_FILE_BATCH;
    }

    public String getCopyIntoOrientMode() {
        return copyIntoOrientMode;
    }

    public void setCopyIntoOrientMode(String copyIntoOrientMod) {
        copyIntoOrientMode = copyIntoOrientMod;
    }
}
