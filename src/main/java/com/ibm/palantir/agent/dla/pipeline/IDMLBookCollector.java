package com.ibm.palantir.agent.dla.pipeline;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.ibm.palantir.agent.dla.entity.FileEntity;
import com.ibm.palantir.agent.dla.extractor.IDMLBookExtractor;
import com.ibm.palantir.agent.dla.loader.IDMLBookLoader;
import com.ibm.palantir.agent.logger.LoggerUtils;
import com.ibm.palantir.agent.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.agent.manager.AgentProperties;
import com.ibm.palantir.agent.manager.RetryProperties;
import com.ibm.palantir.agent.manager.exception.ExceptionSneaker;
import com.ibm.palantir.agent.manager.pipeline.PipelineMode;
import com.ibm.palantir.agent.manager.pipeline.PipelineSneaker;
import com.ibm.palantir.agent.util.FileMatchUtils;
import com.ibm.palantir.agent.util.ZFileUtils;
import com.ibm.palantir.catelyn.config.entity.ExtendItem;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.util.FileUtils;
import com.ibm.palantir.catelyn.util.KeyUtil;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class IDMLBookCollector {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = IDMLBookCollector.class.getSimpleName();
    private static final String FORMAT_TYPE = "IDMLBOOK_FILE";
    private static final String CONTENT = "CONTENT";
    private static final String FORMAT = "FORMAT";
    private static final String DATASET_NAME = "DATASET_NAME";
    private static final String MEMBER_NAME = "MEMBER_NAME";
    private static final String SYSPLEX_NAME = "SYSPLEX_NAME";
    private boolean ZOSBASECount = true;

    public void idmlBookCollector(JsonObject dataJson, PipelineMode pipelineMode) throws ServiceException {
        LOG.log(LogLevel.INFO, CLASSNAME, "idmlBookCollector", "Running idmlBookCollector");

        int intervalTime = RetryProperties.getRetryProperties().getBATCH_INTERVAL_TIME();
        long batchSize = AgentProperties.getAgentProperties().getIDML_FILE_BATCH() * 1024;
        String copyIntoOrientMode = AgentProperties.getAgentProperties().getCopyIntoOrientMode();
        String paramFormatErrorCode = ErrorCode.PluginParamFormatError.getCodeStr();
        List<String> errorEntity = new ArrayList<>();

        if (dataJson.get(FORMAT).getAsString().equals(FORMAT_TYPE)) {
            if (!dataJson.get(CONTENT).isJsonArray()) {
                String paramFormatErrorMsg = MessageFormat.format(MsgTemp.get(paramFormatErrorCode),
                        "JCL", "IDMLBookCollector", "The CONTENT value is not an array");
                LOG.log(LogLevel.DEBUG, CLASSNAME, "idmlBookCollector", "Error: {}", paramFormatErrorMsg);
                throw new ServiceException(paramFormatErrorCode, paramFormatErrorMsg, CLASSNAME, "idmlBookCollector");
            }
            JsonArray jsonArray = dataJson.get(CONTENT).getAsJsonArray();

            JsonArray processArray = new JsonArray();
            IDMLBookExtractor idmlBookExtractor;
            ExtendItem extendItem = getExtendItem(pipelineMode);
            IDMLBookLoader idmlBookLoader = new IDMLBookLoader(extendItem);
            PipelineSneaker pipelineSneaker = PipelineSneaker.getSneaker();
            String uniqueTimeStr = KeyUtil.getUniqueTimeStr();
            long accumulateSize = 0;
            List<FileEntity> fileList = new ArrayList<>();

            for (JsonElement jsonElement : jsonArray) {
                if (!jsonElement.isJsonObject()) {
                    String paramFormatErrorMsg = MessageFormat.format(MsgTemp.get(paramFormatErrorCode),
                            "JCL", "IDMLBookCollector", "The CONTENT entity is not an json object: " + jsonElement);
                    LOG.log(LogLevel.DEBUG, CLASSNAME, "idmlBookCollector", "Error: {}", paramFormatErrorMsg);
                    throw new ServiceException(paramFormatErrorCode, paramFormatErrorMsg, CLASSNAME,
                            "idmlBookCollector");
                }
                JsonObject jsonObject = jsonElement.getAsJsonObject();
                if (!jsonObject.has(DATASET_NAME) || !jsonObject.has(MEMBER_NAME)) {
                    String paramFormatErrorMsg = MessageFormat.format(MsgTemp.get(paramFormatErrorCode),
                            "JCL", "IDMLBookCollector",
                            "There is no DATASET_NAME key or MEMBER_NAME key in the CONTENT entity: " + jsonElement);
                    LOG.log(LogLevel.DEBUG, CLASSNAME, "idmlBookCollector", "Error: {}", paramFormatErrorMsg);
                    throw new ServiceException(paramFormatErrorCode, paramFormatErrorMsg, CLASSNAME,
                            "idmlBookCollector");
                }
                if (!jsonObject.has(SYSPLEX_NAME) || jsonObject.get(SYSPLEX_NAME).isJsonNull()) {
                    String paramFormatErrorMsg = MessageFormat.format(MsgTemp.get(paramFormatErrorCode),
                            "JCL", "IDMLBookCollector",
                            "There is no SYSPLEX_NAME key or the value of SYSPLEX_NAME is null in the CONTENT entity: "
                                    + jsonElement);
                    LOG.log(LogLevel.DEBUG, CLASSNAME, "idmlBookCollector", "Error: {}", paramFormatErrorMsg);
                    throw new ServiceException(paramFormatErrorCode, paramFormatErrorMsg, CLASSNAME,
                            "idmlBookCollector");
                }

                String dataset = jsonObject.get(DATASET_NAME).getAsString().trim();
                String memberName = jsonObject.get(MEMBER_NAME).getAsString().trim();
                String sysplexName = jsonObject.get(SYSPLEX_NAME).getAsString().trim();
                pipelineSneaker.newMember(dataset, dataset);

                try {
                    for (String filePath : FileMatchUtils.getFileList(dataset, memberName, pipelineMode)) {
                        FileEntity fileEntity = new FileEntity(sysplexName, dataset);
                        fileEntity.setFilePath(filePath);
                        fileList.add(fileEntity);
                    }
                } catch (ServiceException e) {
                    String path = pipelineMode.getPath(dataset, memberName);
                    String commonError = ErrorCode.ProcessError.getCodeStr();
                    String commonErrorMsg = MessageFormat.format(MsgTemp.get(commonError),
                            MessageFormat.format(
                                    "For DATASET_NAME {0} and MEMBER_NAME {1}, get file list throw error: {2}",
                                    dataset, memberName, e.getMessage()));
                    LOG.log(LogLevel.ERROR, CLASSNAME, "idmlBookCollector", commonErrorMsg);
                    errorEntity.add(path);
                    ExceptionSneaker.getSneaker().addException(e, CLASSNAME, "idmlBookCollector");
                    continue;
                }
            }

            FileEntity ZOSBASESourceFileEntity = checkZOSBASE(fileList, pipelineMode);
            String ZOSBASESourceFile = ZOSBASESourceFileEntity.getFilePath();
            String ZOSBASEFile;

            try {
                idmlBookExtractor = new IDMLBookExtractor();
                idmlBookExtractor.idmlBookExtractor(ZOSBASESourceFile, pipelineMode);
                pipelineSneaker.loadedNewFile(ZOSBASESourceFile);

                ZOSBASEFile = idmlBookLoader.loadData(ZOSBASESourceFileEntity, idmlBookExtractor, uniqueTimeStr,
                        pipelineMode);
                idmlBookExtractor.closeInputStream();
                pipelineSneaker.addReadRecords(1);
                processArray.add(ZOSBASEFile);
                accumulateSize += idmlBookExtractor.getFileSize();
                System.gc();
            } catch (Exception e) {
                String commonError = ErrorCode.ProcessError.getCodeStr();
                String commonErrorMsg = MessageFormat.format(MsgTemp.get(commonError),
                        MessageFormat.format("Upload ZOSBASE file error in this path {0}: {1}",
                                ZOSBASESourceFile, e.getMessage()));
                LOG.log(LogLevel.DEBUG, CLASSNAME, "idmlBookCollector", "Error: {}", commonErrorMsg);
                errorEntity.add(ZOSBASESourceFile);
                ExceptionSneaker.getSneaker().addException(e, CLASSNAME, "idmlBookCollector");
                throw new ServiceException(commonError, commonErrorMsg, CLASSNAME, "idmlBookCollector");
            }

            for (FileEntity fileEntity : fileList) {
                String filePath = fileEntity.getFilePath();
                try {
                    long startTime = System.currentTimeMillis();
                    idmlBookExtractor = new IDMLBookExtractor();
                    idmlBookExtractor.idmlBookExtractor(filePath, pipelineMode);
                    pipelineSneaker.loadedNewFile(filePath);

                    String savedFileName = idmlBookLoader.loadData(fileEntity, idmlBookExtractor, uniqueTimeStr,
                            pipelineMode);
                    idmlBookExtractor.closeInputStream();
                    pipelineSneaker.addReadRecords(1);
                    processArray.add(savedFileName);
                    accumulateSize += idmlBookExtractor.getFileSize();
                    if (accumulateSize > batchSize) {
                        processData(idmlBookLoader, processArray, pipelineSneaker);
                        accumulateSize = 0;
                        processArray = new JsonArray();
                        processArray.add(ZOSBASEFile);
                        System.gc();
                    }
                    long endTime = System.currentTimeMillis();
                    long deltaTime = endTime - startTime;
                    if (deltaTime < intervalTime) {
                        Thread.sleep(intervalTime - deltaTime);
                    }
                } catch (Exception e) {
                    String commonError = ErrorCode.ProcessError.getCodeStr();
                    String commonErrorMsg = MessageFormat.format(MsgTemp.get(commonError),
                            MessageFormat.format("There is crashed error when process {0} file: {1}",
                                    filePath, e.getMessage()));
                    LOG.log(LogLevel.ERROR, CLASSNAME, "idmlBookCollector", commonErrorMsg);
                    errorEntity.add(filePath);
                    ExceptionSneaker.getSneaker().addException(e, CLASSNAME, "idmlBookCollector");
                    continue;
                }
            }

            try {
                if (accumulateSize != 0) {
                    processData(idmlBookLoader, processArray, pipelineSneaker);
                }
                idmlBookLoader.updateSystemRelationship();
                idmlBookLoader.copyIntoOrientRequest(copyIntoOrientMode);
            } catch (ServiceException e) {
                LOG.log(LogLevel.ERROR, CLASSNAME, "idmlBookCollector", MessageFormat.format(
                        "There is error when processData or updateSystemRelationship in IDMLBookCollector at the closeout phase: {0}",
                        e.getMessage()));
                ExceptionSneaker.getSneaker().addException(e, CLASSNAME, "idmlBookCollector");
            }

            if (!errorEntity.isEmpty()) {
                LOG.log(LogLevel.ERROR, CLASSNAME, "idmlBookCollector", MessageFormat.format(
                        "There are errors in these IDML Book file list: {0}",
                        String.join(",", errorEntity)));
            }
            System.gc();
            return;
        } else {
            String paramFormatErrorMsg = MessageFormat.format(MsgTemp.get(paramFormatErrorCode),
                    "JCL", "IDMLBookCollector", "The Format type is not supported");
            LOG.log(LogLevel.DEBUG, CLASSNAME, "idmlBookCollector", "Error: {}", paramFormatErrorMsg);
            throw new ServiceException(paramFormatErrorCode, paramFormatErrorMsg, CLASSNAME, "idmlBookCollector");
        }
    }

    private ExtendItem getExtendItem(PipelineMode pipelineMode) throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "getExtendItem", "Running getExtendItem with params - pipelineMode: {}",
                pipelineMode);
        List<ExtendItem> itemList = pipelineMode.getPersist();
        for (ExtendItem extendEntity : itemList) {
            String persistType = extendEntity.getType();
            if (persistType.equals("API_DLA")) {
                return extendEntity;
            }
        }
        String errCode = ErrorCode.ProcessError.getCodeStr();
        String msg = MessageFormat.format(MsgTemp.get(errCode),
                "IDMLBookCollectorLoader",
                MessageFormat.format("There are no Persist config whose type is {0}.", "API_DLA"));
        LOG.log(LogLevel.DEBUG, CLASSNAME, "getExtendItem", "Error: {}", msg);
        throw new ServiceException(errCode, msg, CLASSNAME, "getExtendItem");
    }

    private FileEntity checkZOSBASE(List<FileEntity> fileList, PipelineMode pipelineMode) throws ServiceException {
        LOG.log(LogLevel.INFO, CLASSNAME, "checkZOSBASE", "Running checkZOSBASE");
        List<String> zosbaseList = new ArrayList<>();
        FileEntity zosbaseEntity = new FileEntity();
        for (FileEntity fileEntity : fileList) {
            String filePath = fileEntity.getFilePath();
            String fileName;
            if (pipelineMode == PipelineMode.MAINFRAME) {
                fileName = ZFileUtils.getZFileNameFromPath(filePath);
            } else {
                fileName = FileUtils.getFileNameFromPath(filePath);
            }
            if (fileName.toLowerCase().contains("zosbase")) {
                zosbaseList.add(filePath);
                zosbaseEntity = fileEntity;
            }
        }
        if (zosbaseList.size() == 1) {
            fileList.remove(zosbaseEntity);
            return zosbaseEntity;
        } else if (zosbaseList.size() == 0) {
            String commonError = ErrorCode.ProcessError.getCodeStr();
            String commonErrorMsg = MessageFormat.format(MsgTemp.get(commonError),
                    "There is no ZOSBASE file in upload list");
            LOG.log(LogLevel.DEBUG, CLASSNAME, "checkZOSBASE", "Error: {}", commonErrorMsg);
            throw new ServiceException(commonError, commonErrorMsg, CLASSNAME, "checkZOSBASE");
        } else {
            String commonError = ErrorCode.ProcessError.getCodeStr();
            String commonErrorMsg = MessageFormat.format(MsgTemp.get(commonError),
                    MessageFormat.format(
                            "There are {0} ZOSBASE files, not only 1 in upload list. The ZOSBASE files are: {1}",
                            zosbaseList.size(), String.join(",", zosbaseList)));
            LOG.log(LogLevel.DEBUG, CLASSNAME, "checkZOSBASE", "Error: {}", commonErrorMsg);
            throw new ServiceException(commonError, commonErrorMsg, CLASSNAME, "checkZOSBASE");
        }
    }

    private void processData(IDMLBookLoader idmlBookLoader, JsonArray processArray, PipelineSneaker pipelineSneaker) {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "processData",
                "Running processData with params - idmlBookLoader: {}, processArray: {}, pipelineSneaker: {}",
                idmlBookLoader, processArray.toString(), pipelineSneaker.toString());
        try {
            String resStr = idmlBookLoader.processData(processArray);
            checkResponse(resStr, pipelineSneaker);
        } catch (ServiceException e) {
            LOG.log(LogLevel.ERROR, CLASSNAME, "processData",
                    MessageFormat.format("There are errors when trigger PopulateDLA process: {0}", e.getMessage()));
            ExceptionSneaker.getSneaker().addException(e, CLASSNAME, "checkZOSBASE");
        }
    }

    private void checkResponse(String resStr, PipelineSneaker pipelineSneaker) {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "checkResponse",
                "Running checkResponse with params - resStr: {}, pipelineSneaker: {}",
                resStr, pipelineSneaker.toString());
        String[] resLines = resStr.split("\n");
        String pattern = "\\d+";
        for (String line : resLines) {
            line = line.trim();
            if (line.startsWith("Successfully parsed") || line.startsWith("Successfully generated delta ")) {
                int fileNum = 0;
                int objectNum = 0;
                Pattern regex = Pattern.compile(pattern + " files");
                Matcher matcher = regex.matcher(line);
                if (matcher.find()) {
                    String match = matcher.group();
                    fileNum = Integer.parseInt(match.replace(" files", ""));

                }
                regex = Pattern.compile(pattern + " objects");
                matcher = regex.matcher(line);
                if (matcher.find()) {
                    String match = matcher.group();
                    objectNum = Integer.parseInt(match.replace(" objects", ""));
                }
                if (fileNum > 0) {
                    pipelineSneaker.newPersistBatch(fileNum - 1, objectNum);
                    if (ZOSBASECount) {
                        pipelineSneaker.newPersistBatch(1, 0);
                        ZOSBASECount = false;
                    }
                }
            }
            if (line.startsWith("But fails to")) {
                Pattern regex = Pattern.compile(pattern + " files:");
                Matcher matcher = regex.matcher(line);
                if (matcher.find()) {
                    String match = matcher.group();
                    int number = Integer.parseInt(match.replace(" files:", ""));
                    pipelineSneaker.addErrorRecords(number);
                }
                String errCode = ErrorCode.PipelineTaskError.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), "PopulateDLA process", line);
                LOG.log(LogLevel.DEBUG, CLASSNAME, "checkResponse", msg);
                ExceptionSneaker.getSneaker()
                        .addException(new ServiceException(errCode, msg, CLASSNAME, "checkResponse"), CLASSNAME,
                                "checkResponse");
            }
            if (line.startsWith("There contains") && line.contains("unexpected objects")) {
                String errCode = ErrorCode.PipelineTaskError.getCodeStr();
                String msg = MessageFormat.format(MsgTemp.get(errCode), "PopulateDLA process", line);
                LOG.log(LogLevel.DEBUG, CLASSNAME, "checkResponse", msg);
                ExceptionSneaker.getSneaker()
                        .addException(new ServiceException(errCode, msg, CLASSNAME, "checkResponse"), CLASSNAME,
                                "checkResponse");
            }
        }
    }
}
