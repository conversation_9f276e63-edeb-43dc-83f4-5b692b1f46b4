package com.ibm.palantir.agent.dla.loader;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonSyntaxException;
import com.ibm.palantir.agent.dla.entity.FileEntity;
import com.ibm.palantir.agent.dla.extractor.IDMLBookExtractor;
import com.ibm.palantir.agent.logger.LoggerUtils;
import com.ibm.palantir.agent.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.agent.manager.RetryProperties;
import com.ibm.palantir.agent.manager.pipeline.PipelineMode;
import com.ibm.palantir.catelyn.config.entity.ExtendItem;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.util.GsonUtils;
import com.ibm.palantir.catelyn.util.HttpUtils;

public class IDMLBookLoader {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = IDMLBookLoader.class.getSimpleName();
    private Map<String, String> headers;
    private String url;
    private JsonObject option;
    private String authorizationStr;
    private long tokenTimestamp = 0;
    private int tokenDuration = 0;

    public IDMLBookLoader(ExtendItem extendItem) {
        this.headers = new HashMap<>();
        this.headers.put("Accept", "*/*");
        this.headers.put("Content-Type", "application/json");
        // this.headers.put("ApiToken", "098f6bcd4621d373cade4e832627b4f6");
        this.headers.put("ApiToken", extendItem.getPassword());
        this.url = extendItem.getUrl();
        this.option = extendItem.getOption();
    }

    public String loadData(FileEntity fileEntity, IDMLBookExtractor idmlBookExtractor, String directory,
            PipelineMode pipelineMode) throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "loadData",
                "Running loadData with params - fileEntity: {}, directory: {}, pipelineMode: {}",
                fileEntity.toString(), directory, pipelineMode);
        String filePath = fileEntity.getFilePath();
        int retryNumber = RetryProperties.getRetryProperties().getRETRY_NUMBER();
        int retryCount = 0;
        ServiceException historyE = new ServiceException(ErrorCode.HttpRequestError.getCodeStr(),
                "Initial ServiceException.", CLASSNAME, "loadData");
        while (retryCount < retryNumber) {
            try {
                Map<String, String> params = new HashMap<>();
                params.put("sysplexName", fileEntity.getSysplexName());
                params.put("datasetName", fileEntity.getDatasetName());
                params.put("memberName", idmlBookExtractor.getFileName());
                params.put("directory", directory);
                String result = loadDataProcess(idmlBookExtractor.getInputStream(), params, pipelineMode);
                retryCount++;
                return result;
            } catch (ServiceException e) {
                historyE = e;
                String message = e.getMessage();
                if (checkError(message) && retryCount < retryNumber - 1) {
                    idmlBookExtractor = new IDMLBookExtractor();
                    System.gc();
                    idmlBookExtractor.idmlBookExtractor(filePath, pipelineMode);
                    retryCount++;
                    continue;
                } else {
                    throw e;
                }
            }
        }
        throw historyE;
    }

    public String loadDataProcess(InputStream inputStream, Map<String, String> params, PipelineMode pipelineMode)
            throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "loadDataProcess", "Running loadDataProcess");
        processAuthorization();
        String address = this.url;
        String res = upload(address, params.get("memberName"), inputStream, this.headers, params, pipelineMode);
        JsonObject resJson = checkReturn(address, res);
        if (!resJson.has("filePath")) {
            String errCode = ErrorCode.HttpRequestError.getCodeStr();
            String msg = MessageFormat.format("The API result JSON don't have fileName column: {0}", res);
            LOG.log(LogLevel.DEBUG, CLASSNAME, "loadDataProcess", "Error: {}", msg);
            throw new ServiceException(errCode, msg, CLASSNAME, "loadDataProcess");
        }
        return resJson.get("filePath").getAsString();
    }

    public String processData(JsonArray jsonArray) throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "processData", "Running processData");
        int retryNumber = RetryProperties.getRetryProperties().getRETRY_NUMBER();
        int retryCount = 0;
        ServiceException historyE = new ServiceException(ErrorCode.HttpRequestError.getCodeStr(),
                "Initial ServiceException.", CLASSNAME, "processData");
        while (retryCount < retryNumber) {
            try {
                String result = processDataProcess(jsonArray);
                retryCount++;
                return result;
            } catch (ServiceException e) {
                historyE = e;
                String message = e.getMessage();
                if (checkError(message) && retryCount < retryNumber - 1) {
                    retryCount++;
                    continue;
                } else {
                    throw e;
                }
            }
        }
        throw historyE;
    }

    public String processDataProcess(JsonArray jsonArray) throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "processDataProcess",
                "Running processDataProcess with params - jsonArray: {}",
                jsonArray.toString());

        JsonObject jsonObject = new JsonObject();
        jsonObject.add("fileList", jsonArray);


        if (this.option != null && this.option.size() > 0) {

            String populateDLAUrl = this.option.has("populateDLAUrl") ? this.option.get("populateDLAUrl").getAsString() : null;

            // Proceed with the process if either URL exists and is not null/empty
            if ((populateDLAUrl != null && !populateDLAUrl.isEmpty())) {

                if (jsonArray.size() != 0) {
                    processAuthorization();
                    String generateAddress = populateDLAUrl;
                    String res = HttpUtils.post(generateAddress, this.headers, null,
                            GsonUtils.toJsonStringWithNull(jsonObject), true);
                    return checkReturn(generateAddress, res).get("message").getAsString();
                } else {
                    LOG.log(LogLevel.WARN, CLASSNAME, "processDataProcess",
                            "No files were provided for upload. Skipping the data processing step.");
                }
            } else {
                LOG.log(LogLevel.WARN, CLASSNAME, "processDataProcess", "populateDLAUrl is not provided or empty.");
            }
        }


        return "";
    }

    public String updateSystemRelationship() throws ServiceException {
        LOG.log(LogLevel.INFO, CLASSNAME, "updateSystemRelationship", "Running updateSystemRelationship");

        int retryNumber = RetryProperties.getRetryProperties().getRETRY_NUMBER();
        int retryCount = 0;
        ServiceException historyE = new ServiceException(ErrorCode.HttpRequestError.getCodeStr(),
                "Initial ServiceException.", CLASSNAME, "updateSystemRelationship");
        while (retryCount < retryNumber) {
            try {
                String result = updateSystemRelationshipProcess();
                retryCount++;
                return result;
            } catch (ServiceException e) {
                historyE = e;
                String message = e.getMessage();
                if (checkError(message) && retryCount < retryNumber - 1) {
                    retryCount++;
                    continue;
                } else {
                    throw e;
                }
            }
        }
        throw historyE;
    }

    public String updateSystemRelationshipProcess() throws ServiceException {
        LOG.log(LogLevel.INFO, CLASSNAME, "updateSystemRelationshipProcess",
                "Running updateSystemRelationshipProcess");
        if (this.option != null && this.option.size() > 0) {
            if (this.option.has("updateSystemRelationship")) {
                processAuthorization();
                String relationAddress = this.option.get("updateSystemRelationship").getAsString();
                String res = HttpUtils.post(relationAddress, this.headers, null,
                        "{\"FORMAT\": \"JOB_SYSTEM_RELATIONSHIP\", \"CONTENT\": []}", true);
                checkReturn(relationAddress, res);
            }
        }
        return "";
    }

    public String copyIntoOrientRequest(String mode) throws ServiceException {
        if (this.option != null && this.option.size() > 0 && this.option.has("copyIntoOrientUrl")) {
            int retryNumber = RetryProperties.getRetryProperties().getRETRY_NUMBER();
            int retryCount = 0;
            ServiceException historyE = new ServiceException(ErrorCode.HttpRequestError.getCodeStr(),
                    "Initial ServiceException.");
            while (retryCount < retryNumber) {
                try {
                    String res = copyIntoOrientRequestProcess(mode);
                    retryCount++;
                    return res;
                } catch (ServiceException e) {
                    historyE = e;
                    String message = e.getMessage();
                    if (checkError(message) && retryCount < retryNumber - 1) {
                        retryCount++;
                    } else {
                        throw e;
                    }
                }
            }
            throw historyE;
        }
        LOG.log(LogLevel.DEBUG, CLASSNAME, "copyIntoOrientRequest", "The copyIntoOrient process is disabled");
        return "";
    }

    private String copyIntoOrientRequestProcess(String option) throws ServiceException {
        String copyIntoOrientAddress = this.option.get("copyIntoOrientUrl").getAsString();
        processAuthorization();
        String res = HttpUtils.post(copyIntoOrientAddress, this.headers, null, option, true);
        checkReturn(copyIntoOrientAddress, res);
        return res;
    }

    public boolean checkError(String errorMessage) {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "checkError", "Running checkError with params - errorMessage: {}",
                errorMessage);
        int retrySleepTime = RetryProperties.getRetryProperties().getRETRY_SLEEP_TIME();
        if (errorMessage.contains("java.lang.Exception: 429:{")) {
            String responseStr = errorMessage.substring(errorMessage.lastIndexOf("java.lang.Exception: 429:{") + 25);
            try {
                JsonObject resObject = GsonUtils.parseString(responseStr).getAsJsonObject();
                if (!resObject.has("status") || resObject.get("status").getAsInt() != 429) {
                    return false;
                }
                if (resObject.has("path")) {
                    String path = resObject.get("path").getAsString();
                    if (path.startsWith("/zrdds/api/v1")) {
                        LOG.log(LogLevel.WARN, CLASSNAME, "checkError", MessageFormat.format(
                                "Because of '429:Too Many Requests' error for keycloak, sleep {0} millis and then retry.",
                                retrySleepTime));
                        Thread.sleep(retrySleepTime);
                        return true;
                    }
                }
                return false;
            } catch (Exception e) {
                return false;
            }
        } else if (errorMessage.contains("/openid-connect/token: java.lang.Exception: 429:") && errorMessage.contains("(type=Too Many Requests, status=429)")) {
            try {
                LOG.log(LogLevel.WARN, CLASSNAME, "checkError", MessageFormat.format(
                        "Because of '429:Too Many Requests' error for keycloak, sleep {0} millis and then retry.",
                        retrySleepTime));

                Thread.sleep(retrySleepTime);
                return true;
            } catch (Exception e) {
                return false;
            }
        } else if (errorMessage.contains("HttpResponseProxy{HTTP") && errorMessage.contains(" 429 ")
                && errorMessage.contains("X-RateLimit-Limit:")) {
            try {
                LOG.log(LogLevel.WARN, CLASSNAME, "checkError", MessageFormat.format(
                        "Because of '429:Too Many Requests' error for keycloak, sleep {0} millis and then retry.",
                        retrySleepTime));
                Thread.sleep(retrySleepTime);
                return true;
            } catch (Exception e) {
                return false;
            }
        } else if (errorMessage.endsWith("throw error: org.apache.http.client.ClientProtocolException")) {
            try {
                LOG.log(LogLevel.WARN, CLASSNAME, "checkError", MessageFormat.format(
                        "Because of 'org.apache.http.client.ClientProtocolException' error, sleep {0} millis and then retry.",
                        retrySleepTime));

                Thread.sleep(retrySleepTime);
                return true;
            } catch (Exception e) {
                return false;
            }
        }
        return false;
    }

    public JsonObject checkReturn(String url, String res) throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "checkReturn", "Running checkReturn with params - url: {}, res: {}",
                url, res);

        if (res == null || res.isEmpty()) {
            String errCode = ErrorCode.HttpRequestError.getCodeStr();
            String msg = MessageFormat
                    .format("The {0} API return empty message, may caused by keyCloak authorization error.", url);
            LOG.log(LogLevel.DEBUG, CLASSNAME, "checkReturn", "Error: {}", msg);
            throw new ServiceException(errCode, msg, CLASSNAME, "checkReturn");
        } else {
            JsonObject resObject;
            try {
                resObject = GsonUtils.parseString(res).getAsJsonObject();
            } catch (Exception e) {
                String errCode = ErrorCode.HttpRequestError.getCodeStr();
                String msg = MessageFormat.format("The {0} API return error message: {1}", url, res);
                LOG.log(LogLevel.DEBUG, CLASSNAME, "checkReturn", "Error: {}", msg);
                throw new ServiceException(errCode, msg, CLASSNAME, "checkReturn");
            }
            if (resObject.has("statusCode")) {
                String statusCode = resObject.get("statusCode").getAsString();
                if (statusCode.equals("0")) {
                    LOG.log(LogLevel.INFO, CLASSNAME, "checkReturn",
                            MessageFormat.format("The {0} API server return successful message: {1}",
                                    url, resObject.get("message").getAsString()));
                    return resObject;
                } else {
                    String errCode = ErrorCode.HttpRequestError.getCodeStr();
                    String msg = MessageFormat.format("The {0} API return error statusCode {1} with error message: {2}", url, statusCode, resObject.get("message").getAsString());
                    LOG.log(LogLevel.DEBUG, CLASSNAME, "checkReturn", "Error: {}", msg);
                    throw new ServiceException(errCode, msg, CLASSNAME, "checkReturn");
                }
            } else {
                String errCode = ErrorCode.HttpRequestError.getCodeStr();
                String msg = MessageFormat.format("The {0} API return error message: {1}", url, res);
                LOG.log(LogLevel.DEBUG, CLASSNAME, "checkReturn", "Error: {}", msg);
                throw new ServiceException(errCode, msg, CLASSNAME, "checkReturn");
            }
        }
    }

    public void processAuthorization() throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "processAuthorization", "Running processAuthorization");
        if (this.option != null && this.option.size() > 0) {
            if (this.option.has("keycloakClientGrantType")
                    && !this.option.get("keycloakClientGrantType").getAsString().isEmpty()
                    && this.option.has("keycloakTokenUri")
                    && !this.option.get("keycloakTokenUri").getAsString().isEmpty()) {
                long now = System.currentTimeMillis();
                long duration = (now - this.tokenTimestamp) / 1000;
                if (duration > this.tokenDuration) {
                    this.authorizationStr = getAuthorization();
                }
                this.headers.put("Authorization", this.authorizationStr);
                this.headers.remove("ApiToken");
            }
        }
    }

    public String getAuthorization() throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "getAuthorization", "Running getAuthorization");
        String authorizationType = this.option.get("keycloakClientGrantType").getAsString();
        if (authorizationType.equals("client_credentials")) {
            if (this.option.has("keycloakTokenUri") && this.option.has("keycloakClientId")
                    && this.option.has("keycloakClientSecret")) {
                String authorizationStr = getAuthorizationByClientCredentials(
                        this.option.get("keycloakTokenUri").getAsString(),
                        this.option.get("keycloakClientId").getAsString(),
                        this.option.get("keycloakClientSecret").getAsString());
                return authorizationStr;
            } else {
                String errCode = ErrorCode.ConfigurationError.getCodeStr();
                String msg = MessageFormat.format("YAML Configuration Error: {0}",
                        "The API persist option config don't contain keycloakTokenUri, keycloakClientId or keycloakClientSecret.");
                LOG.log(LogLevel.DEBUG, CLASSNAME, "getAuthorization", "Error: {}", msg);
                throw new ServiceException(errCode, msg, CLASSNAME, "getAuthorization");
            }
        } else if (authorizationType.equals("password")) {
            if (this.option.has("keycloakTokenUri") && this.option.has("keycloakClientId")
                    && this.option.has("keycloakClientUsername") && this.option.has("keycloakClientPassword")) {
                String authorizationStr = getAuthorizationByClientPassword(
                        this.option.get("keycloakTokenUri").getAsString(),
                        this.option.get("keycloakClientId").getAsString(),
                        this.option.get("keycloakClientUsername").getAsString(),
                        this.option.get("keycloakClientPassword").getAsString());
                return authorizationStr;
            } else {
                String errCode = ErrorCode.ConfigurationError.getCodeStr();
                String msg = MessageFormat.format("YAML Configuration Error: {0}",
                        "The API persist option config don't contain keycloakTokenUri, keycloakClientId, keycloakClientUsername or keycloakClientPassword.");
                LOG.log(LogLevel.DEBUG, CLASSNAME, "getAuthorization", "Error: {}", msg);
                throw new ServiceException(errCode, msg, CLASSNAME, "getAuthorization");
            }
        } else {
            String errCode = ErrorCode.ConfigurationError.getCodeStr();
            String msg = MessageFormat.format("YAML Configuration Error: {0}",
                    "The keycloakClientGrantType value of API persist option config should be client_credentials or password");
            LOG.log(LogLevel.DEBUG, CLASSNAME, "getAuthorization", "Error: {}", msg);
            throw new ServiceException(errCode, msg, CLASSNAME, "getAuthorization");
        }
    }

    public String getAuthorizationByClientPassword(String url, String clientId, String username, String password)
            throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "getAuthorizationByClientPassword",
                "Running getAuthorizationByClientPassword");

        HashMap<String, String> body = new HashMap<>();
        body.put("client_id", clientId);
        body.put("username", username);
        body.put("password", password);
        body.put("grant_type", "password");

        return executeGetAccessToken(url, body);
    }

    public String getAuthorizationByClientCredentials(String url, String clientId, String clientSecret)
            throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "getAuthorizationByClientCredentials",
                "Running getAuthorizationByClientCredentials");

        HashMap<String, String> body = new HashMap<>();
        body.put("client_id", clientId);
        body.put("client_secret", clientSecret);
        body.put("grant_type", "client_credentials");

        return executeGetAccessToken(url, body);
    }

    private String executeGetAccessToken(String url, HashMap<String, String> body) throws ServiceException {

        LOG.log(LogLevel.DEBUG, CLASSNAME, "executeGetAccessToken", "Running executeGetAccessToken with url: {}", url);

        String bodyStr = HttpUtils.formatMapContent(body, false);
        String res;
        if (url.trim().startsWith("https:")) {
            res = HttpUtils.post(url, null, null, bodyStr, true);
        } else {
            res = HttpUtils.post(url, null, null, bodyStr, false);
        }
        JsonObject tokenObject;
        try {
            tokenObject = GsonUtils.parseString(res).getAsJsonObject();
        } catch (JsonSyntaxException e) {
            String errCode = ErrorCode.HttpRequestError.getCodeStr();
            String msg = MessageFormat.format("The keyCloak server return error: {0}", res);
            LOG.log(LogLevel.DEBUG, CLASSNAME, "executeGetAccessToken", "Error: {}", msg);
            throw new ServiceException(errCode, msg, CLASSNAME, "executeGetAccessToken");
        }
        if (tokenObject.has("access_token") && tokenObject.has("token_type") && tokenObject.has("expires_in")) {
            int expires_in = tokenObject.get("expires_in").getAsInt();
            if (expires_in > 10) {
                this.tokenDuration = expires_in - 10;
                this.tokenTimestamp = System.currentTimeMillis();
            }
            String token = tokenObject.get("token_type").getAsString() + " "
                    + tokenObject.get("access_token").getAsString();
            return token;
        } else {
            if (tokenObject.has("error") && tokenObject.has("error_description")) {
                String errCode = ErrorCode.HttpRequestError.getCodeStr();
                String msg = MessageFormat.format("The keyCloak server return {0} error with error_description: {1}", tokenObject.get("error").getAsString(), tokenObject.get("error_description").getAsString());
                LOG.log(LogLevel.DEBUG, CLASSNAME, "executeGetAccessToken", "Error: {}", msg);
                throw new ServiceException(errCode, msg, CLASSNAME, "executeGetAccessToken");
            } else {
                String errCode = ErrorCode.HttpRequestError.getCodeStr();
                String msg = MessageFormat.format("The keyCloak server return Error: {0}", res);
                LOG.log(LogLevel.DEBUG, CLASSNAME, "executeGetAccessToken", "Error: {}", msg);
                throw new ServiceException(errCode, msg, CLASSNAME, "executeGetAccessToken");
            }
        }
    }

    private String upload(String url, String fileName, InputStream inputStream, Map<String, String> headerParams,
            Map<String, String> otherParams, PipelineMode pipelineMode) throws ServiceException {

        LOG.log(LogLevel.INFO, CLASSNAME, "upload", "Running upload with url: {}, fileName: {}, pipelineMode: {}", url,
                fileName, pipelineMode);

        CloseableHttpClient httpClient = null;
        try {
            httpClient = HttpClients.custom()
                    .setSSLSocketFactory(new SSLConnectionSocketFactory(
                            SSLContexts.custom().loadTrustMaterial(null, new TrustSelfSignedStrategy()).build(),
                            NoopHostnameVerifier.INSTANCE))
                    .build();
            HttpPost httpPost = new HttpPost(url);

            if (Objects.nonNull(headerParams)) {
                for (Map.Entry<String, String> e : headerParams.entrySet()) {
                    httpPost.addHeader(e.getKey(), e.getValue());
                }
            }
            httpPost.removeHeader(httpPost.getFirstHeader("Content-Type"));
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.setCharset(StandardCharsets.UTF_8);
            builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);

            builder = builder.addBinaryBody("file", inputStream, ContentType.MULTIPART_FORM_DATA, fileName);
            if (Objects.nonNull(otherParams)) {
                for (Map.Entry<String, String> e : otherParams.entrySet()) {
                    // add params info
                    builder.addTextBody(e.getKey(), e.getValue(),
                            ContentType.create("text/plain", StandardCharsets.UTF_8));
                }
            }
            HttpEntity entity = builder.build();
            httpPost.setEntity(entity);
            HttpResponse response = httpClient.execute(httpPost);
            HttpEntity responseEntity = response.getEntity();
            inputStream.close();
            if (response.getStatusLine().getStatusCode() == 200) {
                String body = EntityUtils.toString(responseEntity);
                return body;
            }
            return response.toString();
        } catch (ClientProtocolException e) {
            String errCode = ErrorCode.HttpRequestError.getCodeStr();
            String msg = MessageFormat.format("The upload file request {0} throw error: {1}",
                    url, e.toString());
            String errorMsg = MessageFormat.format(MsgTemp.get(errCode), url, msg);
            LOG.log(LogLevel.DEBUG, CLASSNAME, "upload", "Error: {}", errorMsg);
            throw new ServiceException(errCode, errorMsg, CLASSNAME, "upload");
        } catch (Exception e) {
            String errCode = ErrorCode.HttpRequestError.getCodeStr();
            String msg = MessageFormat.format("The upload file request {0} throw error: {1}",
                    url, e.toString());
            String errorMsg = MessageFormat.format(MsgTemp.get(errCode), url, msg);
            LOG.log(LogLevel.DEBUG, CLASSNAME, "upload", "Error: {}", errorMsg);
            throw new ServiceException(errCode, errorMsg, CLASSNAME, "upload");
        } finally {
            if (Objects.nonNull(httpClient)) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    String errCode = ErrorCode.HttpRequestError.getCodeStr();
                    String errorMsg = MessageFormat.format(MsgTemp.get(errCode), url, "HttpClient close IO error");
                    LOG.log(LogLevel.DEBUG, CLASSNAME, "upload", "Error: {}", errorMsg);
                    throw new ServiceException(errCode, errorMsg, CLASSNAME, "upload");
                }
            }
        }
    }
}
