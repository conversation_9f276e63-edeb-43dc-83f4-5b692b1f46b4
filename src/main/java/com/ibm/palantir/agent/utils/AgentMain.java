/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2026
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

package com.ibm.palantir.agent.utils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.format.ResolverStyle;
import java.util.Arrays;

import com.ibm.palantir.agent.utils.exception.ErrorCode;
import com.ibm.palantir.agent.utils.exception.ServiceException;
import com.ibm.palantir.agent.utils.logger.LoggerUtils;
import com.ibm.palantir.agent.utils.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.agent.utils.manager.ExtendItem;
import com.ibm.palantir.agent.utils.manager.ParamParser;
import com.ibm.palantir.agent.utils.manager.PipelineMode;
import com.ibm.palantir.agent.utils.manager.Process;
import com.ibm.palantir.agent.utils.manager.RetryProperties;

public class AgentMain {

    static {
        System.setProperty("logback.configurationFile", "./internal_config/log.xml");
        LoggerUtils.setREPO("Discovery-Agent-Utils");
    }

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = AgentMain.class.getSimpleName();
    private static final String ARG_LENGTH_AT_LEAST_2 = "The arguments length should be at least 2";
    private static final String ARG_LENGTH_2 = "The arguments length should be 2";
    private static final String RESEND_ERROR_MESSAGE = """
            Incorrect number of arguments for resendExpiredDLAItems.
            Expected usage: config/user_config.yml <startDate> <endDate>

            NOTE:
            1. <startDate> and <endDate> must strictly follow the format YYYY-MM-DD.
            2. Both dates should be passed as a single parameter, separated by a space.
            3. Example in ZAGREDEL JCL script: SET OTHPARM='2026-01-01 2026-01-10'

            Example usage: config/user_config.yml 2026-01-01 2026-01-10
            """;

    public static void main(String[] args) throws Exception {
        LOG.log(LogLevel.INFO, CLASSNAME, "main", "Running main with Params: {}", String.join(" ", args));
        String pattern = "%";
        String startDate = null;
        String endDate = null;

        if (args.length < 2) {
            LOG.log(LogLevel.DEBUG, CLASSNAME, "main", "Error: {}", ARG_LENGTH_AT_LEAST_2);
            throw new ServiceException(ErrorCode.IncorrectArgsLength.getCodeStr(), ARG_LENGTH_AT_LEAST_2, CLASSNAME,
                    "main");
        }

        GetProcessName processName = GetProcessName.from(args[1]);

        if (processName == null) {
            LOG.log(LogLevel.ERROR, CLASSNAME, "main", "Invalid process name: '{}'", args[1]);
            throw new IllegalArgumentException("Invalid process name: " + args[1]);
        }

        switch (processName) {

        case GET_JOB_NAME -> {
            LOG.log(LogLevel.DEBUG, CLASSNAME, "main", "getJobName args length {}", args.length);
            if (args.length > 3) {
                String errMsg = "Incorrect number of arguments \n getJobName parameters should be config/user_config.yml <pattern>\n Example : config/user_config.yml %S";
                LOG.log(LogLevel.ERROR, CLASSNAME, "main", errMsg);
                throw new Exception(errMsg);
            }
            if (args.length == 3) {
                String patternArg = args[2].trim();
                patternArg = patternArg.isEmpty() ? "%" : patternArg;
                if (!validatePattern(patternArg)) {
                    String errMsg = "Invalid pattern: it should follow SQL LIKE syntax. Example: '%JOB%'";
                    LOG.log(LogLevel.ERROR, CLASSNAME, "main", errMsg);
                    throw new IllegalArgumentException(errMsg);
                }
                pattern = patternArg;
            }
        }
        case RESEND_EXPIRED_DLA_ITEMS -> {
            LOG.log(LogLevel.DEBUG, CLASSNAME, "main", "resendExpiredDLAItems args length {}", args.length);

            if (args.length != 3) {
                LOG.log(LogLevel.ERROR, CLASSNAME, "main", RESEND_ERROR_MESSAGE);
                throw new ServiceException(ErrorCode.IncorrectArgsLength.getCodeStr(), RESEND_ERROR_MESSAGE, CLASSNAME,
                        "main");
            }

            String[] dateArgs = Arrays.stream(args[2].trim().split("\\s+")).map(String::trim).toArray(String[]::new);
            validateDates(dateArgs);
            startDate = dateArgs[0];
            endDate = dateArgs[1];

            LOG.log(LogLevel.INFO, CLASSNAME, "main", "Start Date: {}, End Date: {}", startDate, endDate);
        }
        default -> {
            if (args.length != 2) {
                LOG.log(LogLevel.DEBUG, CLASSNAME, "main", "Error: {}", ARG_LENGTH_2);
                throw new ServiceException(ErrorCode.IncorrectArgsLength.getCodeStr(), ARG_LENGTH_2, CLASSNAME, "main");
            }
        }
        }

        RetryProperties retryProperties = RetryProperties.getRetryProperties();
        retryProperties.setValueFromSystemSetting();
        PipelineMode pipelineMode = getPipelineMode();
        ParamParser paramParser = new ParamParser();
        ExtendItem extendItem = paramParser.paramParser(args[0], pipelineMode);
        Process process = new Process(extendItem);

        LOG.log(LogLevel.INFO, CLASSNAME, "main", "The {} process has started", processName);

        switch (processName) {
        case GET_JOB_NAME -> process.getJCLRecord(pattern, 0L, 0L);
        case GET_RECORD_NUMBER -> LOG.log(LogLevel.INFO, CLASSNAME, "main", process.getRecordNumber());
        case DETECT_EXPIRED_DLA_ITEMS -> LOG.log(LogLevel.INFO, CLASSNAME, "main", process.postDetect());
        case RESEND_EXPIRED_DLA_ITEMS -> LOG.log(LogLevel.INFO, CLASSNAME, "main",
                process.resendExpiredDLAItems(startDate, endDate));
        default -> LOG.log(LogLevel.ERROR, CLASSNAME, "main",
                "The parameter of this process should be getJobName/getRecordNumber/detectExpiredDLAItems/resendExpiredDLAItems");
        }

        LOG.log(LogLevel.INFO, CLASSNAME, "main", "The {} process has ended", processName);
    }

    private static PipelineMode getPipelineMode() {
        PipelineMode pipelineMode;
        String osName = System.getProperty("os.name");
        if (osName.startsWith("z/OS")) {
            pipelineMode = PipelineMode.MAINFRAME;
        } else {
            pipelineMode = PipelineMode.DISTRIBUTE;
        }
        return pipelineMode;
    }

    private static boolean validatePattern(String patternArg) {
        char firstChar = patternArg.charAt(0);
        return firstChar == '%' || firstChar == '_' || Character.isLetterOrDigit(firstChar);
    }

    private enum GetProcessName {
        GET_JOB_NAME("getJobName"), GET_RECORD_NUMBER("getRecordNumber"),
        DETECT_EXPIRED_DLA_ITEMS("detectExpiredDLAItems"), RESEND_EXPIRED_DLA_ITEMS("resendExpiredDLAItems");

        private final String value;

        GetProcessName(String value) {
            this.value = value;
        }

        static GetProcessName from(String arg) {
            for (GetProcessName cmd : values()) {
                if (cmd.value.equals(arg)) {
                    return cmd;
                }
            }
            return null;
        }
    }

    private static void validateDates(String[] dates) throws ServiceException {
        if (dates.length != 2) {
            LOG.log(LogLevel.ERROR, CLASSNAME, "validateDates", RESEND_ERROR_MESSAGE);
            throw new ServiceException(ErrorCode.IncorrectArgsLength.getCodeStr(), RESEND_ERROR_MESSAGE, CLASSNAME,
                    "validateDates");
        }

        String start = dates[0].trim();
        String end = dates[1].trim();

        if (start.isBlank() || end.isBlank()) {
            String errMsg = "Start date or end date cannot be empty. Example: 2026-01-16 2026-01-18";
            LOG.log(LogLevel.ERROR, CLASSNAME, "validateDates", errMsg);
            throw new ServiceException(ErrorCode.IncorrectArgsLength.getCodeStr(), errMsg, CLASSNAME, "validateDates");
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("uuuu-MM-dd").withResolverStyle(ResolverStyle.STRICT);
        LocalDate startDate;
        LocalDate endDate;

        try {
            startDate = LocalDate.parse(start, formatter);
        } catch (DateTimeParseException e) {
            String errMsg = "Invalid start date. Expected a valid date in YYYY-MM-DD format: " + start;
            LOG.log(LogLevel.ERROR, CLASSNAME, "validateDates", errMsg);
            throw new ServiceException(ErrorCode.PluginParamFormatError.getCodeStr(), errMsg, CLASSNAME,
                    "validateDates");
        }

        try {
            endDate = LocalDate.parse(end, formatter);
        } catch (DateTimeParseException e) {
            String errMsg = "Invalid end date. Expected a valid date in YYYY-MM-DD format: " + end;
            LOG.log(LogLevel.ERROR, CLASSNAME, "validateDates", errMsg);
            throw new ServiceException(ErrorCode.PluginParamFormatError.getCodeStr(), errMsg, CLASSNAME,
                    "validateDates");
        }

        if (endDate.isBefore(startDate)) {
            String errMsg = "End date cannot be before start date. Start: " + start + ", End: " + end;
            LOG.log(LogLevel.ERROR, CLASSNAME, "validateDates", errMsg);
            throw new ServiceException(ErrorCode.PluginParamFormatError.getCodeStr(), errMsg, CLASSNAME,
                    "validateDates");
        }
    }

}