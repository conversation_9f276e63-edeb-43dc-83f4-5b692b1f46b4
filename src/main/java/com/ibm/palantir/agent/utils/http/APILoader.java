package com.ibm.palantir.agent.utils.http;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

import org.json.JSONObject;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonSyntaxException;
import com.ibm.palantir.agent.utils.exception.ErrorCode;
import com.ibm.palantir.agent.utils.exception.ServiceException;
import com.ibm.palantir.agent.utils.logger.LoggerUtils;
import com.ibm.palantir.agent.utils.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.agent.utils.manager.ExtendItem;
import com.ibm.palantir.agent.utils.manager.GsonUtils;
import com.ibm.palantir.agent.utils.manager.RetryProperties;

public class APILoader {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();

    private static final String CLASSNAME = APILoader.class.getSimpleName();

    private static final String API = "/zrdds/api/v1";

    private static final String KEYCLOAK = "/auth/realms/IzoaKeycloak/protocol/openid-connect/token";

    private Map<String, String> headers;

    private String url;

    private JsonObject option;

    private String authorizationStr;

    private long tokenTimestamp = 0;
    private int tokenDuration = 0;

    public APILoader(ExtendItem extendItem) {
        this.headers = new HashMap<>();
        this.headers.put("Accept", "application/json");
        this.headers.put("Content-Type", "application/json");
        this.headers.put("ApiToken", extendItem.getPassword());
        this.url = extendItem.getUrl();
        this.option = extendItem.getOption();
    }

    public String getRequest(String path) throws ServiceException {
        int retryNumber = RetryProperties.getRetryProperties().getRETRY_NUMBER();
        int retryCount = 0;
        ServiceException historyE = new ServiceException(ErrorCode.HttpRequestError.getCodeStr(),
                "Initial ServiceException.", CLASSNAME, "getRequest");
        while (retryCount < retryNumber) {
            try {
                String res = getRequestProcess(path);
                retryCount++;
                return res;
            } catch (ServiceException e) {
                historyE = e;
                String message = e.getMessage();
                if (checkError(message) && retryCount < retryNumber - 1) {
                    retryCount++;
                    continue;
                } else {
                    throw e;
                }
            }
        }
        throw historyE;
    }

    public JSONObject getJCLRecordRequest(String path, String pattern, Long numberOfRecords, Long offset)
            throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "getJCLRecordRequest",
                "Running getJCLRecordRequest with path: {}, pattern: {}, numberOfRecords: {}, offset: {}", path,
                pattern, numberOfRecords, offset);
        int retryNumber = RetryProperties.getRetryProperties().getRETRY_NUMBER();
        int retryCount = 0;
        ServiceException historyE = new ServiceException(ErrorCode.HttpRequestError.getCodeStr(),
                "Initial ServiceException.", CLASSNAME, "getJCLRecordRequest");
        while (retryCount < retryNumber) {
            try {
                JSONObject res = getJCLRecordRequestProcess(path, pattern, numberOfRecords, offset);
                retryCount++;
                return res;
            } catch (ServiceException e) {
                historyE = e;
                String message = e.getMessage();
                if (checkError(message) && retryCount < retryNumber - 1) {
                    retryCount++;
                    continue;
                } else {
                    throw e;
                }
            }
        }
        throw historyE;
    }

    private String getRequestProcess(String path) throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "getRequestProcess",
                "Running getRequestProcess with path: {}", path);
        String address = this.url + path;
        processAuthorization();
        String res = HttpUtils.get(address, this.headers, null, true);
        checkGetReturn(address, res);
        return res;
    }

    private JSONObject getJCLRecordRequestProcess(String path, String pattern, Long numberOfRecords, Long offset)
            throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "getJCLRecordRequestProcess",
                "Running getJCLRecordRequestProcess with path: {}", path);
        Map<String, String> params = new HashMap<>();
        if (pattern != null && !pattern.trim().isEmpty())
            params.put("pattern", pattern);
        params.put("numberOfRecords", String.valueOf(numberOfRecords));
        params.put("offset", String.valueOf(offset));
        String address = this.url + path;
        processAuthorization();

        String res = HttpUtils.get(address, this.headers, params, true);

        checkGetReturn(address, res);

        // Parse the JSON response
        JSONObject jsonResponse = new JSONObject(res);
        String status = jsonResponse.getString("status");
        JSONObject body = jsonResponse.getJSONObject("body");

        if (status.equals("OK") && body != null && body.has("records")) {
            return jsonResponse;
        }
        return new JSONObject();
    }

    public String postRequest(String data) throws ServiceException {
        LOG.log(LogLevel.INFO, CLASSNAME, "postRequest", "Running postRequest with data: {}", data);
        int retryNumber = RetryProperties.getRetryProperties().getRETRY_NUMBER();
        int retryCount = 0;
        ServiceException historyE = new ServiceException(ErrorCode.HttpRequestError.getCodeStr(),
                "Initial ServiceException.", CLASSNAME, "postRequest");
        while (retryCount < retryNumber) {
            try {
                String res = postRequestProcess(data);
                retryCount++;
                return res;
            } catch (ServiceException e) {
                historyE = e;
                String message = e.getMessage();
                if (checkError(message) && retryCount < retryNumber - 1) {
                    retryCount++;
                    continue;
                } else {
                    throw e;
                }
            }
        }
        throw historyE;
    }

    private String postRequestProcess(String data) throws ServiceException {
        LOG.log(LogLevel.INFO, CLASSNAME, "postRequestProcess", "Running postRequestProcess with data: {}", data);
        if (this.option != null && this.option.size() > 0 && this.option.has("detectExpiredDLAItemsUrl")) {
            String generateAddress = this.option.get("detectExpiredDLAItemsUrl").getAsString();
            processAuthorization();
            String res = HttpUtils.post(generateAddress, this.headers, null, data, true);
            checkProcessReturn(generateAddress, res);
            return res;
        } else {
            String errCode = ErrorCode.ConfigurationError.getCodeStr();
            String msg = MessageFormat.format("YAML Configuration Error: {0}",
                    "The detectExpiredDLAItemsUrl is missing");
            LOG.log(LogLevel.DEBUG, CLASSNAME, "postRequestProcess", "Error: {}", msg);
            throw new ServiceException(errCode, msg, CLASSNAME, "postRequestProcess");
        }
    }

    public String resendExpiredDLAItemsPostRequest(String requestBody) throws ServiceException {
        int retryNumber = RetryProperties.getRetryProperties().getRETRY_NUMBER();
        int retryCount = 0;
        ServiceException lastException = new ServiceException(ErrorCode.HttpRequestError.getCodeStr(),
                "Initial ServiceException");

        LOG.log(LogLevel.DEBUG, CLASSNAME, "resendExpiredDLAItemsPostRequest",
                "Starting resendExpiredDLAItemsPostRequest with retry limit {}", retryNumber);

        while (retryCount < retryNumber) {
            try {
                String response = resendExpiredDLAItemsPostRequestProcess(requestBody);
                LOG.log(LogLevel.INFO, CLASSNAME, "resendExpiredDLAItemsPostRequest",
                        "Successfully resent expired DLA items on attempt {}", retryCount + 1);
                return response;
            } catch (ServiceException e) {
                lastException = e;
                retryCount++;

                if (checkError(e.getMessage()) && retryCount < retryNumber) {
                    LOG.log(LogLevel.WARN, CLASSNAME, "resendExpiredDLAItemsPostRequest",
                            "Retrying resendExpiredDLAItemsPostRequest (attempt {}/{}) due to error", retryCount,
                            retryNumber);
                } else {
                    LOG.log(LogLevel.ERROR, CLASSNAME, "resendExpiredDLAItemsPostRequest",
                            "Failed to resend expired DLA items after {} attempts", retryCount, e);
                    throw e;
                }
            }
        }
        LOG.log(LogLevel.ERROR, CLASSNAME, "resendExpiredDLAItemsPostRequest",
                "Exhausted all retry attempts ({}) while resending expired DLA items", retryNumber, lastException);
        throw lastException;
    }

    private String resendExpiredDLAItemsPostRequestProcess(String requestBody) throws ServiceException {
        if (this.option != null && !this.option.isEmpty() && this.option.has("resendExpiredDLAItemsUrl")) {
            String address = this.option.get("resendExpiredDLAItemsUrl").getAsString();
            processAuthorization();
            LOG.log(LogLevel.DEBUG, CLASSNAME, "resendExpiredDLAItemsPostRequestProcess",
                    "Sending POST request to {}", address);

            String response = HttpUtils.post(address, this.headers, null, requestBody, true);
            checkProcessReturn(address, response);
            return response;
        }

        String errCode = ErrorCode.ConfigurationError.getCodeStr();
        String msg = "YAML Configuration Error: The resendExpiredDLAItemsUrl is missing";
        LOG.log(LogLevel.ERROR, CLASSNAME, "resendExpiredDLAItemsPostRequestProcess", msg);
        throw new ServiceException(errCode, msg, CLASSNAME, "resendExpiredDLAItemsPostRequestProcess");
    }

    public boolean checkError(String errorMessage) {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "checkError", "Running checkError with errorMessage: {}", errorMessage);
        int retrySleepTime = RetryProperties.getRetryProperties().getRETRY_SLEEP_TIME();
        if (errorMessage.contains("java.lang.Exception: 429:{")) {
            String responseStr = errorMessage.substring(errorMessage.lastIndexOf("java.lang.Exception: 429:{") + 25);
            try {
                JsonObject resObject = GsonUtils.parseString(responseStr).getAsJsonObject();
                if (!resObject.has("status") || resObject.get("status").getAsInt() != 429) {
                    return false;
                }
                if (resObject.has("path")) {
                    String path = resObject.get("path").getAsString();
                    if (path.startsWith("/zrdds/api/v1")) {
                        LOG.log(LogLevel.WARN, CLASSNAME, "checkError", MessageFormat.format(
                                "Because of '429:Too Many Requests' error for keycloak, sleep {0} millis and then retry.",
                                retrySleepTime));
                        Thread.sleep(retrySleepTime);
                        return true;
                    }
                }
                return false;
            } catch (Exception e) {
                return false;
            }
        } else if (errorMessage.contains("/openid-connect/token: java.lang.Exception: 429:") &&
                errorMessage.contains("(type=Too Many Requests, status=429)")) {
            try {
                LOG.log(LogLevel.WARN, CLASSNAME, "checkError", MessageFormat.format(
                        "Because of '429:Too Many Requests' error for keycloak, sleep {0} millis and then retry.",
                        retrySleepTime));
                Thread.sleep(retrySleepTime);
                return true;
            } catch (Exception e) {
                return false;
            }
        } else {
            return false;
        }
    }

    public void checkGetReturn(String url, String res) throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "checkGetReturn", "Running checkGetReturn with res: {}", res);
        if (res == null || res.isEmpty()) {
            String errCode = ErrorCode.HttpRequestError.getCodeStr();
            String msg = MessageFormat
                    .format("The {0} API return empty message, may caused by keyCloak authorization error", url);
            LOG.log(LogLevel.DEBUG, CLASSNAME, "checkGetReturn", "Error: {}", msg);
            throw new ServiceException(errCode, msg, CLASSNAME, "checkGetReturn");
        } else {
            JsonElement resElement = GsonUtils.parseString(res);
            if (resElement.isJsonNull()) {
                String errCode = ErrorCode.HttpRequestError.getCodeStr();
                String msg = MessageFormat.format("The {0} API return empty message: {1}", url, res);
                LOG.log(LogLevel.DEBUG, CLASSNAME, "checkGetReturn", "Error: {}", msg);
                throw new ServiceException(errCode, msg, CLASSNAME, "checkGetReturn");
            } else {
                return;
            }
        }
    }

    public void checkProcessReturn(String url, String res) throws ServiceException {
        if (res == null || res.isEmpty()) {
            String errCode = ErrorCode.HttpRequestError.getCodeStr();
            String msg = MessageFormat
                    .format("The {0} API return empty message, may caused by keyCloak authorization error", url);
            LOG.log(LogLevel.DEBUG, CLASSNAME, "checkProcessReturn", "Error: {}", msg);
            throw new ServiceException(errCode, msg, CLASSNAME, "checkProcessReturn");
        } else {
            JsonObject resObject = GsonUtils.parseString(res).getAsJsonObject();
            if (resObject.has("statusCode")) {
                String statusCode = resObject.get("statusCode").getAsString();
                if (statusCode.equals("0")) {
                    LOG.log(LogLevel.INFO, CLASSNAME, "checkProcessReturn",
                            MessageFormat.format("The {0} API server return successful message: {1}",
                                    url, resObject.get("message").getAsString()));
                    return;
                } else {
                    String errCode = ErrorCode.HttpRequestError.getCodeStr();
                    String msg = MessageFormat.format("The {0} API return error statusCode {1} with error message: {2}",
                            url, statusCode, resObject.get("message").getAsString());
                    LOG.log(LogLevel.DEBUG, CLASSNAME, "checkProcessReturn", "Error: {}", msg);
                    throw new ServiceException(errCode, msg, CLASSNAME, "checkProcessReturn");
                }
            } else {
                String errCode = ErrorCode.HttpRequestError.getCodeStr();
                String msg = MessageFormat.format("The {0} API return error message: {1}",
                        url, res);
                LOG.log(LogLevel.DEBUG, CLASSNAME, "checkProcessReturn", "Error: {}", msg);
                throw new ServiceException(errCode, msg, CLASSNAME, "checkProcessReturn");
            }
        }
    }

    public void processAuthorization() throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "processAuthorization", "Running processAuthorization");
        if (this.option != null && this.option.size() > 0) {
            if (this.option.has("keycloakClientGrantType")
                    && !this.option.get("keycloakClientGrantType").getAsString().isEmpty()
                    && this.option.has("keycloakTokenUri")
                    && !this.option.get("keycloakTokenUri").getAsString().isEmpty()) {
                long now = System.currentTimeMillis();
                long duration = (now - this.tokenTimestamp) / 1000;
                if (duration > this.tokenDuration) {
                    this.authorizationStr = getAuthorization();
                }
                this.headers.put("Authorization", this.authorizationStr);
                this.headers.remove("ApiToken");
            }
        }
    }

    public String getAuthorization() throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "getAuthorization", "Running getAuthorization");
        String authorizationType = this.option.get("keycloakClientGrantType").getAsString();
        if (authorizationType.equals("client_credentials")) {
            if (this.option.has("keycloakTokenUri") && this.option.has("keycloakClientId")
                    && this.option.has("keycloakClientSecret")) {
                String authorizationStr = getAuthorizationByClientCredentials(
                        this.option.get("keycloakTokenUri").getAsString(),
                        this.option.get("keycloakClientId").getAsString(),
                        this.option.get("keycloakClientSecret").getAsString());
                return authorizationStr;
            } else {
                String errCode = ErrorCode.ConfigurationError.getCodeStr();
                String msg = MessageFormat.format("YAML Configuration Error: {0}",
                        "The API persist option config don't contain keycloakTokenUri, keycloakClientId or keycloakClientSecret.");
                LOG.log(LogLevel.DEBUG, CLASSNAME, "getAuthorization", "Error: {}", msg);
                throw new ServiceException(errCode, msg, CLASSNAME, "getAuthorization");
            }
        } else if (authorizationType.equals("password")) {
            if (this.option.has("keycloakTokenUri") && this.option.has("keycloakClientId")
                    && this.option.has("keycloakClientUsername") && this.option.has("keycloakClientPassword")) {
                String authorizationStr = getAuthorizationByClientPassword(
                        this.option.get("keycloakTokenUri").getAsString(),
                        this.option.get("keycloakClientId").getAsString(),
                        this.option.get("keycloakClientUsername").getAsString(),
                        this.option.get("keycloakClientPassword").getAsString());
                return authorizationStr;
            } else {
                String errCode = ErrorCode.ConfigurationError.getCodeStr();
                String msg = MessageFormat.format("YAML Configuration Error: {0}",
                        "The API persist option config don't contain keycloakTokenUri, keycloakClientId,keycloakClientUsername or keycloakClientPassword.");
                LOG.log(LogLevel.DEBUG, CLASSNAME, "getAuthorization", "Error: {}", msg);
                throw new ServiceException(errCode, msg, CLASSNAME, "getAuthorization");
            }
        } else {
            String errCode = ErrorCode.ConfigurationError.getCodeStr();
            String msg = MessageFormat.format("YAML Configuration Error: {0}",
                    "The keycloakClientGrantType value of API persist option config should be client_credentials or password");
            LOG.log(LogLevel.DEBUG, CLASSNAME, "getAuthorization", "Error: {}", msg);
            throw new ServiceException(errCode, msg, CLASSNAME, "getAuthorization");
        }
    }

    public String getAuthorizationByClientPassword(String url, String clientId, String username, String password)
            throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "getAuthorizationByClientPassword",
                "Running getAuthorizationByClientPassword");

        HashMap<String, String> body = new HashMap<>();
        body.put("client_id", clientId);
        body.put("username", username);
        body.put("password", password);
        body.put("grant_type", "password");

        return executeGetAccessToken(url, body);
    }

    public String getAuthorizationByClientCredentials(String url, String clientId, String clientSecret)
            throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "getAuthorizationByClientCredentials",
                "Running getAuthorizationByClientCredentials");

        HashMap<String, String> body = new HashMap<>();
        body.put("client_id", clientId);
        body.put("client_secret", clientSecret);
        body.put("grant_type", "client_credentials");

        return executeGetAccessToken(url, body);
    }

    private String executeGetAccessToken(String url, HashMap<String, String> body) throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "executeGetAccessToken", "Running executeGetAccessToken");

        String bodyStr = HttpUtils.formatMapContent(body, false);
        String res;
        if (url.trim().startsWith("https:")) {
            res = HttpUtils.post(url, null, null, bodyStr, true);
        } else {
            res = HttpUtils.post(url, null, null, bodyStr, false);
        }
        JsonObject tokenObject;
        try {
            tokenObject = GsonUtils.parseString(res).getAsJsonObject();
        } catch (JsonSyntaxException e) {
            String errCode = ErrorCode.HttpRequestError.getCodeStr();
            String msg = MessageFormat.format("The keyCloak server return error: {0}", res);
            LOG.log(LogLevel.DEBUG, CLASSNAME, "executeGetAccessToken", "Error: {}", msg);
            throw new ServiceException(errCode, msg, CLASSNAME, "executeGetAccessToken");
        }
        if (tokenObject.has("access_token") && tokenObject.has("token_type") && tokenObject.has("expires_in")) {
            int expires_in = tokenObject.get("expires_in").getAsInt();
            if (expires_in > 10) {
                this.tokenDuration = expires_in - 10;
                this.tokenTimestamp = System.currentTimeMillis();
            }
            String token = tokenObject.get("token_type").getAsString() + " "
                    + tokenObject.get("access_token").getAsString();
            return token;
        } else {
            if (tokenObject.has("error") && tokenObject.has("error_description")) {
                String errCode = ErrorCode.HttpRequestError.getCodeStr();
                String msg = MessageFormat.format("The keyCloak server return {0} error with error_description: {1}",
                        tokenObject.get("error").getAsString(), tokenObject.get("error_description").getAsString());
                LOG.log(LogLevel.DEBUG, CLASSNAME, "executeGetAccessToken", "Error: {}", msg);
                throw new ServiceException(errCode, msg, CLASSNAME, "executeGetAccessToken");
            } else {
                String errCode = ErrorCode.HttpRequestError.getCodeStr();
                String msg = MessageFormat.format("The keyCloak server return Error: {0}", res);
                LOG.log(LogLevel.DEBUG, CLASSNAME, "executeGetAccessToken", "Error: {}", msg);
                throw new ServiceException(errCode, msg, CLASSNAME, "executeGetAccessToken");
            }
        }
    }
}
