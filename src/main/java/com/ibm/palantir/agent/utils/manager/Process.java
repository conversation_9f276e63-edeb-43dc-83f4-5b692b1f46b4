package com.ibm.palantir.agent.utils.manager;

import org.json.JSONArray;
import org.json.JSONObject;

import com.ibm.palantir.agent.utils.exception.ServiceException;
import com.ibm.palantir.agent.utils.http.APILoader;
import com.ibm.palantir.agent.utils.logger.LoggerUtils;
import com.ibm.palantir.agent.utils.logger.LoggerUtils.LogLevel;

public class Process {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();

    private static final String CLASSNAME = Process.class.getSimpleName();

    private static final String JOB_NAME = "/manage/jclJobName";

    private static final String RECORD_NUMBER = "/manage/recordNumber";

    private APILoader apiLoader;

    StringBuilder stringBuilder = new StringBuilder();

    Long jobCount = 0L;

    private static final int CHAR_LIMIT = AgentProperties.getAgentProperties().getJobLineCharLimit();

    private static final String PREFIX = AgentProperties.getAgentProperties().getGetjobnameLinePrefix();

    public Process(ExtendItem extendItem) {
        this.apiLoader = new APILoader(extendItem);
    }

    public void getJCLRecord(String pattern, Long numberOfRecords, Long offset) throws ServiceException {
        LOG.log(LogLevel.DEBUG, CLASSNAME, "getJCLRecord",
                "getJCLRecord parameters : pattern: {}, numberOfRecords: {}, offset: {}", pattern, numberOfRecords,
                offset);
        JSONObject jsonResponse = this.apiLoader.getJCLRecordRequest(JOB_NAME, pattern, numberOfRecords, offset);
        String status = jsonResponse.getString("status");
        JSONObject body = jsonResponse.getJSONObject("body");

        if (!(status.equals("OK") && body != null && body.has("records"))) {
            jsonResponse = new JSONObject();
        }

        if (numberOfRecords < 0) {
            Long distintJobCount = body.getLong("distintJobCount");
            System.out.println("Total number of records: " + distintJobCount);
        } else {
            if (body.has("records")) {
                JSONArray records = body.getJSONArray("records");
                jobCount += records.length();
                LOG.log(LogLevel.DEBUG, CLASSNAME, "getJCLRecord",
                        "\n TOTAL_NUMBER_RECORDS : {} \n RECORD_OFFSET : {} \n RECORD_RETURNED : {}",
                        body.getInt("total_number_of_records"), body.getInt("records_offset"),
                        body.getInt("records_returned"));

                if (records.length() > 0) {
                    for (int i = 0; i < records.length(); i++) {
                        String job = records.getString(i) + ",";
                        if (stringBuilder.length() + job.length() > CHAR_LIMIT) {
                            while (stringBuilder.length() > 0) {
                                LOG.log(LogLevel.INFO, CLASSNAME, "getJCLRecord",
                                        (PREFIX.trim().isEmpty() ? "" : (PREFIX + ": "))
                                                + stringBuilder.substring(0,
                                                        Math.min(CHAR_LIMIT, stringBuilder.length())));
                                stringBuilder.delete(0, Math.min(CHAR_LIMIT, stringBuilder.length()));
                            }
                        }
                        stringBuilder.append(job);
                    }
                }
            }
        }

        if (body.has("next") && body.getString("next") != null && !body.getString("next").isEmpty()) {
            String nextPageLink = body.getString("next");
            String[] segments = nextPageLink.split("/");
            if (segments.length >= 2) {
                String offsetStr = String.valueOf(segments[segments.length - 1]);
                String numberOfRecordsStr = String.valueOf(segments[segments.length - 2]);
                try {
                    Long nextOffset = Long.parseLong(offsetStr);
                    Long nextNumberOfRecords = Long.parseLong(numberOfRecordsStr);
                    if (nextNumberOfRecords != -2) {
                        getJCLRecord(pattern, nextNumberOfRecords, nextOffset);
                    } else {
                        printRemainingJobs(pattern);
                    }
                } catch (NumberFormatException e) {
                    LOG.log(LogLevel.ERROR, CLASSNAME, "getJCLRecord",
                            "Invalid offset format in nextPageLink: " + offsetStr + " {}", e);
                }
            }
        } else {
            printRemainingJobs(pattern);
        }

    }

    public String getRecordNumber() throws ServiceException {
        LOG.log(LogLevel.INFO, CLASSNAME, "getRecordNumber", "Calling getRecordNumber with RECORD_NUMBER API: {}",
                RECORD_NUMBER);
        return this.apiLoader.getRequest(RECORD_NUMBER);
    }

    public String postDetect() throws ServiceException {
        AgentProperties agentProperties = AgentProperties.getAgentProperties();
        LOG.log(LogLevel.INFO, CLASSNAME, "postDetect", "Calling postRequest with interval: {}",
                agentProperties.getEXPIRED_INTERVAL());
        return this.apiLoader.postRequest("{\"interval\":" + agentProperties.getEXPIRED_INTERVAL() + "}");
    }

    public String resendExpiredDLAItems(String startDate, String endDate) throws ServiceException {
        return this.apiLoader.resendExpiredDLAItemsPostRequest(
                "{\"startDate\":\"" + startDate + "\",\"endDate\":\"" + endDate + "\"}");
    }

    public void printRemainingJobs(String pattern) {
        if (stringBuilder.length() > 0) {
            if (stringBuilder.length() < CHAR_LIMIT) {
                String remaining = (stringBuilder.length() > 0
                        && stringBuilder.charAt(stringBuilder.length() - 1) == ',')
                                ? stringBuilder.substring(0, stringBuilder.length() - 1)
                                : stringBuilder.toString();

                LOG.log(LogLevel.INFO, CLASSNAME, "getJCLRecord",
                        (PREFIX.trim().isEmpty() ? "" : (PREFIX + ": ")) + remaining);
                stringBuilder.setLength(0);
            } else {
                while (stringBuilder.length() > 0) {
                    LOG.log(LogLevel.INFO, CLASSNAME, "getJCLRecord",
                            (PREFIX.trim().isEmpty() ? "" : (PREFIX + ": "))
                                    + stringBuilder.substring(0, Math.min(CHAR_LIMIT, stringBuilder.length())));
                    stringBuilder.delete(0, Math.min(CHAR_LIMIT, stringBuilder.length()));
                }
            }
        }
        String patternText = pattern != null && !pattern.isEmpty() && !pattern.equals("%")
                ? " matching the pattern: '" + pattern + "'"
                : "";
        String msg = jobCount > 0
                ? "printed " + jobCount + " job(s)"
                        + patternText
                : "No jobs found"
                        + patternText;
        LOG.log(LogLevel.INFO, CLASSNAME, "getJCLRecord", "Successfully completed getJobName process and {} ", msg);
    }
}
