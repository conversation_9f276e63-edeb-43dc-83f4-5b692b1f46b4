/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.sansa.loader;

import com.ibm.palantir.catelyn.jaxb.AppProcessPool;
import com.ibm.palantir.catelyn.jaxb.NetBindAddress;
import com.ibm.palantir.catelyn.jaxb.NetFqdn;
import com.ibm.palantir.catelyn.jaxb.NetIpAddress;
import com.ibm.palantir.catelyn.jaxb.NetIpInterface;
import com.ibm.palantir.catelyn.jaxb.NetTcpPort;
import com.ibm.palantir.catelyn.jaxb.NetUdpPort;
import com.ibm.palantir.catelyn.jaxb.SysZOSAddressSpace;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.AddressSpace;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.BindAddress;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Fqdn;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IpAddress;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.IpInterface;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.ProcessPool;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.TcpPort;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.UdpPort;
import com.ibm.palantir.catelyn.jpa.repository.dla.AddressSpaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.BindAddressRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.FqdnRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IpAddressRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IpInterfaceRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ProcessPoolRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.TcpPortRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.UdpPortRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.utils.StringUtils;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class ZOSTaskLoader {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = ZOSTaskLoader.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private final VertexMapperProcessor vmProcessor = new VertexMapperProcessor();
    private final StringUtils stringUtils = new StringUtils();

    private final AddressSpaceRepository addressSpaceRepository;
    private final BindAddressRepository bindAddressRepository;
    private final FqdnRepository fqdnRepository;
    private final IpAddressRepository ipAddressRepository;
    private final IpInterfaceRepository ipInterfaceRepository;
    private final ProcessPoolRepository processPoolRepository;
    private final TcpPortRepository tcpPortRepository;
    private final UdpPortRepository udpPortRepository;

    public ZOSTaskLoader(AddressSpaceRepository addressSpaceRepository, BindAddressRepository bindAddressRepository,
            FqdnRepository fqdnRepository, IpAddressRepository ipAddressRepository,
            IpInterfaceRepository ipInterfaceRepository, ProcessPoolRepository processPoolRepository,
            TcpPortRepository tcpPortRepository, UdpPortRepository udpPortRepository) {
        this.addressSpaceRepository = addressSpaceRepository;
        this.bindAddressRepository = bindAddressRepository;
        this.fqdnRepository = fqdnRepository;
        this.ipAddressRepository = ipAddressRepository;
        this.ipInterfaceRepository = ipInterfaceRepository;
        this.processPoolRepository = processPoolRepository;
        this.tcpPortRepository = tcpPortRepository;
        this.udpPortRepository = udpPortRepository;
    }

    public List<String> loadAddressSpaceList(List<Object> addressSpaceElements, String prefixId, String scanDate) {
        return saveAddressSpaceList(createAddressSpaceList(addressSpaceElements, prefixId, scanDate));
    }

    private List<AddressSpace> createAddressSpaceList(List<Object> addressSpaceElements, String prefixId,
            String scanDate) {
        List<AddressSpace> addressSpaceList = new ArrayList<>();

        for (Object element : addressSpaceElements) {
            SysZOSAddressSpace sysZOSAddressSpace = (SysZOSAddressSpace) element;
            AddressSpace addressSpace = new AddressSpace();
            vmProcessor.mapFilter(sysZOSAddressSpace, addressSpace);
            addressSpace.setPrefixId(prefixId);
            addressSpace.setScanDate(scanDate);
            addressSpace.setId(stringUtils.composeUniqueId(sysZOSAddressSpace.getId(), prefixId));
            addressSpace.setKafkaSendDate(Instant.now());
            addressSpaceList.add(addressSpace);
        }
        return addressSpaceList;
    }

    private List<String> saveAddressSpaceList(List<AddressSpace> addressSpaceList) {
        // If it raises error return a list containing dlaId(s), else return a empty
        // list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            addressSpaceRepository.saveAll(addressSpaceList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveAddressSpaceList",
                    "Failed to saveAll addressSpaceList, try to save them one-by-one");
            for (AddressSpace ads : addressSpaceList) {
                try {
                    addressSpaceRepository.save(ads);
                } catch (Exception ie) {
                    catchException("AddressSpace", failedDlaIds, ads.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    public HashMap<String, List<String>> loadBindAddressList(List<Object> bindAddressElements, String prefixId,
            String scanDate) {
        List<String> successIds = new ArrayList<>();
        List<String> failedDlaIds = new ArrayList<>();
        saveBindAddressList(successIds, createBindAddressList(bindAddressElements, prefixId, scanDate, successIds),
                failedDlaIds);
        return constructDlaIdsMap(successIds, failedDlaIds);
    }

    private List<BindAddress> createBindAddressList(List<Object> bindAddressElements, String prefixId, String scanDate,
            List<String> successIds) {
        List<BindAddress> bindAddressList = new ArrayList<>();

        for (Object element : bindAddressElements) {
            NetBindAddress netBindAddress = (NetBindAddress) element;
            BindAddress bindAddress = new BindAddress();
            vmProcessor.mapFilter(netBindAddress, bindAddress);
            bindAddress.setPrefixId(prefixId);
            bindAddress.setScanDate(scanDate);
            bindAddress.setId(stringUtils.composeUniqueId(netBindAddress.getId(), prefixId));
            bindAddress.setKafkaSendDate(Instant.now());
            bindAddressList.add(bindAddress);
            successIds.add(netBindAddress.getId());
        }
        return bindAddressList;
    }

    private void saveBindAddressList(List<String> successIds, List<BindAddress> bindAddressList,
            List<String> failedDlaIds) {
        // If it raises error return a list containing dlaId(s), else return a empty
        // list.
        try {
            bindAddressRepository.saveAll(bindAddressList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveBindAddressList",
                    "Failed to saveAll bindAddressList, try to save them one-by-one");
            for (BindAddress bdad : bindAddressList) {
                try {
                    bindAddressRepository.save(bdad);
                } catch (Exception ie) {
                    logSaveFailure("BindAddress", failedDlaIds, successIds, bdad.getDlaId(), ie);
                }
            }
        }

    }

    public Object loadFqdn(NetFqdn netFqdn, String prefixId, String scanDate) {
        Fqdn fqdn = new Fqdn();
        vmProcessor.mapFilter(netFqdn, fqdn);
        fqdn.setPrefixId(prefixId);
        fqdn.setScanDate(scanDate);
        fqdn.setId(stringUtils.composeUniqueId(netFqdn.getId(), prefixId));
        fqdn.setKafkaSendDate(Instant.now());
        try {
            return fqdnRepository.save(fqdn);
        } catch (Exception e) {
            return logException("Fqdn", fqdn.getDlaId(), e);
        }
    }

    public List<String> loadFqdnList(List<Object> fqdnElements, String prefixId, String scanDate) {
        return saveFqdnList(getFqdnList(fqdnElements, prefixId, scanDate));
    }

    private List<String> saveFqdnList(List<Fqdn> fqdnList) {
        // If it raises error return a list containing dlaId(s), else return a empty
        // list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            fqdnRepository.saveAll(fqdnList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveFqdnList",
                    "Failed to saveAll fqdnList, try to save them one-by-one");
            for (Fqdn f : fqdnList) {
                try {
                    fqdnRepository.save(f);
                } catch (Exception ie) {
                    catchException("Fqdn", failedDlaIds, f.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<Fqdn> getFqdnList(List<Object> fqdnElements, String prefixId, String scanDate) {
        List<Fqdn> fqdnList = new ArrayList<>();

        for (Object element : fqdnElements) {
            NetFqdn netFqdn = (NetFqdn) element;
            Fqdn fqdn = new Fqdn();
            vmProcessor.mapFilter(netFqdn, fqdn);
            fqdn.setPrefixId(prefixId);
            fqdn.setScanDate(scanDate);
            fqdn.setId(stringUtils.composeUniqueId(netFqdn.getId(), prefixId));
            fqdn.setKafkaSendDate(Instant.now());
            fqdnList.add(fqdn);
        }
        return fqdnList;
    }

    public HashMap<String, List<String>> loadIpAddressList(List<Object> ipAddressElements, String prefixId,
            String scanDate) {
        List<String> successIds = new ArrayList<>();
        List<String> failedDlaIds = new ArrayList<>();
        saveIpAddressList(successIds, failedDlaIds,
                createIpAddressList(ipAddressElements, prefixId, scanDate, successIds));

        return constructDlaIdsMap(successIds, failedDlaIds);

    }

    private void saveIpAddressList(List<String> successIds, List<String> failedDlaIds, List<IpAddress> ipAddressList) {
        // If it raises error return a list containing dlaId(s), else return a empty
        // list.
        try {
            ipAddressRepository.saveAll(ipAddressList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveIpAddressList",
                    "Failed to saveAll ipAddressList, try to save them one-by-one");
            for (IpAddress ipa : ipAddressList) {
                try {
                    ipAddressRepository.save(ipa);
                } catch (Exception ie) {
                    logSaveFailure("IpAddress", failedDlaIds, successIds, ipa.getDlaId(), ie);
                }
            }
        }
    }

    private List<IpAddress> createIpAddressList(List<Object> ipAddressElements, String prefixId, String scanDate,
            List<String> successIds) {
        List<IpAddress> ipAddressList = new ArrayList<>();
        for (Object element : ipAddressElements) {
            NetIpAddress netIpAddress = (NetIpAddress) element;
            IpAddress ipAddress = new IpAddress();
            vmProcessor.mapFilter(netIpAddress, ipAddress);
            ipAddress.setPrefixId(prefixId);
            ipAddress.setScanDate(scanDate);
            ipAddress.setId(stringUtils.composeUniqueId(netIpAddress.getId(), prefixId));
            ipAddress.setKafkaSendDate(Instant.now());
            ipAddressList.add(ipAddress);
            successIds.add(netIpAddress.getId());
        }
        return ipAddressList;
    }

    public HashMap<String, List<String>> loadIpInterfaceList(List<Object> ipInterfaceElements, String prefixId,
            String scanDate) {
        List<String> successIds = new ArrayList<>();
        List<String> failedDlaIds = new ArrayList<>();
        saveIpInterfaceList(successIds, failedDlaIds,
                createIpInterfaceList(ipInterfaceElements, prefixId, scanDate, successIds));
        return constructDlaIdsMap(successIds, failedDlaIds);
    }

    private void saveIpInterfaceList(List<String> successIds, List<String> failedDlaIds,
            List<IpInterface> ipInterfaceList) {
        // If it raises error return a list containing dlaId(s), else return a empty
        // list.
        try {
            ipInterfaceRepository.saveAll(ipInterfaceList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveIpInterfaceList",
                    "Failed to saveAll ipInterfaceList, try to save them one-by-one");
            for (IpInterface ipif : ipInterfaceList) {
                try {
                    ipInterfaceRepository.save(ipif);
                } catch (Exception ie) {
                    logSaveFailure("IpInterface", failedDlaIds, successIds, ipif.getDlaId(), ie);
                }
            }
        }
    }

    private List<IpInterface> createIpInterfaceList(List<Object> ipInterfaceElements, String prefixId, String scanDate,
            List<String> successIds) {
        List<IpInterface> ipInterfaceList = new ArrayList<>();
        for (Object element : ipInterfaceElements) {
            NetIpInterface netIpInterface = (NetIpInterface) element;
            IpInterface ipInterface = new IpInterface();
            vmProcessor.mapFilter(netIpInterface, ipInterface);
            ipInterface.setPrefixId(prefixId);
            ipInterface.setScanDate(scanDate);
            ipInterface.setId(stringUtils.composeUniqueId(netIpInterface.getId(), prefixId));
            ipInterface.setKafkaSendDate(Instant.now());
            ipInterfaceList.add(ipInterface);
            successIds.add(netIpInterface.getId());
        }
        return ipInterfaceList;
    }

    public List<String> loadProcessPoolList(List<Object> processPoolElements, String prefixId, String scanDate) {
        return saveProcessPoolList(createProcessPoolList(processPoolElements, prefixId, scanDate));
    }

    private List<String> saveProcessPoolList(List<ProcessPool> processPoolList) {
        // If it raises error return a list containing dlaId(s), else return a empty
        // list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            processPoolRepository.saveAll(processPoolList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveProcessPoolList",
                    "Failed to saveAll processPoolList, try to save them one-by-one");
            for (ProcessPool pp : processPoolList) {
                try {
                    processPoolRepository.save(pp);
                } catch (Exception ie) {
                    catchException("ProcessPool", failedDlaIds, pp.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<ProcessPool> createProcessPoolList(List<Object> processPoolElements, String prefixId,
            String scanDate) {
        List<ProcessPool> processPoolList = new ArrayList<>();

        for (Object element : processPoolElements) {
            AppProcessPool appProcessPool = (AppProcessPool) element;
            ProcessPool processPool = new ProcessPool();
            vmProcessor.mapFilter(appProcessPool, processPool);
            processPool.setPrefixId(prefixId);
            processPool.setScanDate(scanDate);
            processPool.setId(stringUtils.composeUniqueId(appProcessPool.getId(), prefixId));
            processPool.setKafkaSendDate(Instant.now());
            processPoolList.add(processPool);
        }
        return processPoolList;
    }

    public List<String> loadTcpPortList(List<Object> tcpPortElements, String prefixId, String scanDate) {
        return saveTcpPortList(createTcpPortList(tcpPortElements, prefixId, scanDate));
    }

    private List<String> saveTcpPortList(List<TcpPort> tcpPortList) {
        // If it raises error return a list containing dlaId(s), else return a empty
        // list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            tcpPortRepository.saveAll(tcpPortList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveTcpPortList",
                    "Failed to saveAll tcpPortList, try to save them one-by-one");
            for (TcpPort tp : tcpPortList) {
                try {
                    tcpPortRepository.save(tp);
                } catch (Exception ie) {
                    catchException("TcpPort", failedDlaIds, tp.getDlaId(), ie);
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveTcpPortList", "Failed to save TcpPort: {}",
                            tp.getDlaId());
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveTcpPortList", ie.toString());
                    failedDlaIds.add(tp.getDlaId());
                }
            }
        }
        return failedDlaIds;
    }

    private List<TcpPort> createTcpPortList(List<Object> tcpPortElements, String prefixId, String scanDate) {
        List<TcpPort> tcpPortList = new ArrayList<>();

        for (Object element : tcpPortElements) {
            NetTcpPort netTcpPort = (NetTcpPort) element;

            TcpPort tcpPort = new TcpPort();
            vmProcessor.mapFilter(netTcpPort, tcpPort);
            tcpPort.setPrefixId(prefixId);
            tcpPort.setScanDate(scanDate);
            tcpPort.setId(stringUtils.composeUniqueId(netTcpPort.getId(), prefixId));
            tcpPort.setKafkaSendDate(Instant.now());
            tcpPortList.add(tcpPort);
        }
        return tcpPortList;
    }

    public List<String> loadUdpPortList(List<Object> udpPortElements, String prefixId, String scanDate) {
        return saveUdpPortList(createUdpPortList(udpPortElements, prefixId, scanDate));
    }

    private List<String> saveUdpPortList(List<UdpPort> udpPortList) {
        // If it raises error return a list containing dlaId(s), else return a empty
        // list.
        List<String> failedDlaIds = new ArrayList<>();
        try {
            udpPortRepository.saveAll(udpPortList);
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveUdpPortList",
                    "Failed to saveAll udpPortList, try to save them one-by-one");
            for (UdpPort up : udpPortList) {
                try {
                    udpPortRepository.save(up);
                } catch (Exception ie) {
                    catchException("UdpPort", failedDlaIds, up.getDlaId(), ie);
                }
            }
        }
        return failedDlaIds;
    }

    private List<UdpPort> createUdpPortList(List<Object> udpPortElements, String prefixId, String scanDate) {
        List<UdpPort> udpPortList = new ArrayList<>();

        for (Object element : udpPortElements) {
            NetUdpPort netUdpPort = (NetUdpPort) element;

            UdpPort udpPort = new UdpPort();
            vmProcessor.mapFilter(netUdpPort, udpPort);
            udpPort.setPrefixId(prefixId);
            udpPort.setScanDate(scanDate);
            udpPort.setId(stringUtils.composeUniqueId(netUdpPort.getId(), prefixId));
            udpPort.setKafkaSendDate(Instant.now());
            udpPortList.add(udpPort);
        }
        return udpPortList;
    }

    private void catchException(String entityName, List<String> failedDlaIds, String dlaId, Exception ie) {
        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "catchException", "Failed to save {} with DlaId: {}", entityName,
                dlaId);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "catchException", ie.toString());
        failedDlaIds.add(dlaId);
    }

    private HashMap<String, List<String>> constructDlaIdsMap(List<String> successIds, List<String> failedDlaIds) {
        HashMap res = new HashMap();
        res.put("successIds", successIds);
        res.put("failedIds", failedDlaIds);
        return res;
    }

    // Method to log failure and update success/failure lists
    private void logSaveFailure(String entityName, List<String> failedDlaIds, List<String> successDlaIds, String dlaId,
            Exception ie) {
        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "logSaveFailure", "Failed to save {} with DlaId: {}", entityName,
                dlaId);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "logSaveFailure", ie.toString());
        successDlaIds.remove(dlaId);
        failedDlaIds.add(dlaId);
    }

    private Object logException(String entityName, String dlaId, Exception ie) {
        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "logException", "Failed to save {} with DlaId: {}", entityName,
                dlaId);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "logException", ie.toString());
        return null;
    }
}
