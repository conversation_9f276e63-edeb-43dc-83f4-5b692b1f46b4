/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2025
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.sansa.loader;

import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSFile;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSProgram;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSRegion;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTable;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSSystemInitTableOverride;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.CICSTransaction;
import com.ibm.palantir.catelyn.jpa.entity.dla.postgresql.Relationship;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSFileRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSProgramRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableOverrideRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSSystemInitTableRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.sansa.utils.StringUtils;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringTokenizer;

public class ZReportFileLoader {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = ZReportFileLoader.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private final static String LINE_DELIMITER = "\\r?\\n";
    private final StringUtils stringUtils = new StringUtils();

    private final CICSSystemInitTableRepository cicsSystemInitTableRepository;
    private final CICSSystemInitTableOverrideRepository cicsSystemInitTableOverrideRepository;
    private final CICSProgramRepository cicsProgramRepository;
    private final CICSTransactionRepository cicsTransactionRepository;
    private final CICSFileRepository cicsFileRepository;
    private final RelationshipRepository relationshipRepository;

    public ZReportFileLoader(CICSSystemInitTableRepository cicsSystemInitTableRepository,
            CICSSystemInitTableOverrideRepository cicsSystemInitTableOverrideRepository,
            CICSProgramRepository cicsProgramRepository, CICSTransactionRepository cicsTransactionRepository,
            CICSFileRepository cicsFileRepository,
            RelationshipRepository relationshipRepository) {
        this.cicsSystemInitTableRepository = cicsSystemInitTableRepository;
        this.cicsSystemInitTableOverrideRepository = cicsSystemInitTableOverrideRepository;
        this.cicsProgramRepository = cicsProgramRepository;
        this.cicsTransactionRepository = cicsTransactionRepository;
        this.cicsFileRepository = cicsFileRepository;
        this.relationshipRepository = relationshipRepository;
    }

    public Object loadCICSSit(String dlaId, String name, String content, String prefixId, String scanDate) {
        CICSSystemInitTable cicsSit = new CICSSystemInitTable();
        cicsSit.setDlaId(dlaId);
        cicsSit.setPrefixId(prefixId);
        cicsSit.setName(name);
        cicsSit.setScanDate(scanDate);
        cicsSit.setId(stringUtils.composeUniqueId(dlaId, prefixId));
        Map<String, String> object = resolveMapContent(content);
        cicsSit.setSitApplicationId(object.get("APPLID"));
        cicsSit.setSitCICSSvc(object.get("CICSSVC"));
        cicsSit.setSitCPsConn(object.get("CPSMCONN")); // TODO: need check this param
        cicsSit.setSitCSDAcc(object.get("CSDACC"));
        cicsSit.setSitCSDRls(object.get("CSDRLS"));
        cicsSit.setSitDefaultUser(object.get("DFLTUSER"));
        cicsSit.setSitGMText(object.get("GMTEXT"));
        cicsSit.setSitGMTransaction(object.get("GMTRAN"));
        cicsSit.setSitIRC(object.get("IRCSTRT"));
        cicsSit.setSitISC(object.get("ISC"));
        cicsSit.setSitJVMProfileDirectory(object.get("JVMPROFILEDIR"));
        cicsSit.setKafkaSendDate(Instant.now());
        try {
            return cicsSystemInitTableRepository.save(cicsSit);
        } catch (Exception e) {
            return logException("CICSSystemInitTable", cicsSit.getDlaId(), e);
        }
        // }
    }

    public Object loadCICSSitOverrides(String dlaId, String name, String content, String prefixId, String scanDate) {
        CICSSystemInitTableOverride cicsSitOveride = new CICSSystemInitTableOverride();
        cicsSitOveride.setDlaId(dlaId);
        cicsSitOveride.setPrefixId(prefixId);
        cicsSitOveride.setName(name);
        cicsSitOveride.setScanDate(scanDate);
        cicsSitOveride.setId(stringUtils.composeUniqueId(dlaId, prefixId));
        Map<String, String> object = resolveMapContent(content);
        cicsSitOveride.setSitApplicationId(object.get("APPLID"));
        cicsSitOveride.setSitCICSSvc(object.get("CICSSVC"));
        cicsSitOveride.setSitCPsConn(object.get("CPSMCONN")); // TODO: need check this param
        cicsSitOveride.setSitCSDAcc(object.get("CSDACC"));
        cicsSitOveride.setSitCSDRls(object.get("CSDRLS"));
        cicsSitOveride.setSitDefaultUser(object.get("DFLTUSER"));
        cicsSitOveride.setSitGMText(object.get("GMTEXT"));
        cicsSitOveride.setSitGMTransaction(object.get("GMTRAN"));
        cicsSitOveride.setSitIRC(object.get("IRCSTRT"));
        cicsSitOveride.setSitISC(object.get("ISC"));
        cicsSitOveride.setSitJVMProfileDirectory(object.get("JVMPROFILEDIR"));
        cicsSitOveride.setKafkaSendDate(Instant.now());
        try {
            return cicsSystemInitTableOverrideRepository.save(cicsSitOveride);
        } catch (Exception e) {
            return logException("CICSSystemInitTableOverride", cicsSitOveride.getDlaId(), e);
        }
    }

    public Integer loadPrograms(String content, CICSRegion cicsRegion, String smfId, String scanDate) {
        List<CICSProgram> programList = new ArrayList<>();
        List<Relationship> relationshipList = new ArrayList<>();
        String prefixId = createRelationshipList(content, cicsRegion, smfId, scanDate, programList,
                relationshipList);
        return saveRelationshipList(cicsRegion, programList, relationshipList, prefixId,
                scanDate);
    }

    private String createRelationshipList(String content, CICSRegion cicsRegion, String smfId, String scanDate,
            List<CICSProgram> programList, List<Relationship> relationshipList) {
        String prefixId = cicsRegion.getPrefixId();

        List<List<String>> itemList = resolveListContent(content, 3);
        for (List<String> item : itemList) {

            String name = item.get(0);
            String dlaId = String.format("%s-%s-%s-CICSProgram", name, cicsRegion.getJobName(), smfId);
            CICSProgram program = new CICSProgram();
            program.setDlaId(dlaId);
            program.setPrefixId(prefixId);
            program.setId(stringUtils.composeUniqueId(dlaId, prefixId));
            program.setProgramName(name);
            // TODO: where is the prog_desc and prog_group
            // program_call will be added by process AD data
            program.setProgramLanguage(item.get(2));
            program.setProgramDataLocation(item.get(7));
            program.setProgramExecutionKey(item.get(8));
            program.setScanDate(scanDate);
            program.setKafkaSendDate(Instant.now());
            programList.add(program);

            String programId = String.join("-", prefixId, dlaId);
            relationshipList.add(
                    new Relationship("contains", cicsRegion.getId(), programId, cicsRegion.getDlaId(), dlaId,
                            prefixId, "", "DLA", scanDate, Instant.now()));
        }
        return prefixId;
    }

    private Integer saveRelationshipList(CICSRegion cicsRegion, List<CICSProgram> programList,
            List<Relationship> relationshipList, String prefixId, String scanDate) {
        try {
            cicsProgramRepository.saveAll(programList);
            relationshipRepository.saveAll(relationshipList);
            return programList.size();
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveRelationshipList",
                    "Failed to saveAll CICSProgramList from ZReportFile, try to save them one-by-one");
            Integer c = 0;
            for (CICSProgram cp : programList) {
                try {
                    cicsProgramRepository.save(cp);
                    Relationship rs = new Relationship("contains", cicsRegion.getId(), cp.getId(),
                            cicsRegion.getDlaId(), cp.getDlaId(), prefixId, "", "DLA",
                            scanDate,
                            Instant.now());
                    relationshipRepository.save(rs);
                    c += 1;
                } catch (Exception ie) {
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveRelationshipList",
                            "Failed to save CICSProgram from ZReportFile, name: {}", cp.getProgramName());
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveRelationshipList", ie.toString());
                }
            }
            return c;
        }
    }

    public Integer loadTransactions(String content, CICSRegion cicsRegion, String smfId, String scanDate) {
        List<CICSTransaction> transactionList = new ArrayList<>();
        List<Relationship> relationshipList = new ArrayList<>();
        String prefixId = cicsRegion.getPrefixId();
        createCICSTransactionList(content, cicsRegion, smfId, scanDate, transactionList, relationshipList,
                prefixId);
        return saveCICSTransactionList(cicsRegion, smfId, transactionList, relationshipList, prefixId, scanDate);
    }

    private void createCICSTransactionList(String content, CICSRegion cicsRegion, String smfId, String scanDate,
            List<CICSTransaction> transactionList, List<Relationship> relationshipList, String prefixId) {
        List<List<String>> itemList = resolveTransactionsListContent(content, 3);
        for (List<String> item : itemList) {
            String name = item.get(0);
            String dlaId = String.format("%s-%s-%s-CICSTransaction", name, cicsRegion.getJobName(), smfId);
            String transactionId = stringUtils.composeUniqueId(dlaId, prefixId);

            String programName = item.get(1);
            CICSTransaction transaction = new CICSTransaction();
            transaction.setDlaId(dlaId);
            transaction.setPrefixId(prefixId);
            transaction.setTransactionName(name);
            transaction.setInitialProgram(programName);
            transaction.setDataLocation(item.get(3));
            transaction.setDataKey(item.get(4));
            transaction.setScanDate(scanDate);
            transaction.setId(transactionId);
            transaction.setKafkaSendDate(Instant.now());
            transactionList.add(transaction);

            relationshipList.add(
                    new Relationship("contains", cicsRegion.getId(), transactionId, cicsRegion.getDlaId(),
                            dlaId, prefixId, "", "DLA", scanDate, Instant.now()));
            // the programName maybe empty
            if (!programName.equals("")) {
                // <cdm:uses source="CADP-CICSCWA1-SYS-CICSTransaction"
                // target="DFHDPLU-CICSCWA1-SYS-CICSProgram"/>
                String programDlaId = String.format("%s-%s-%s-CICSProgram", programName, cicsRegion.getJobName(),
                        smfId);
                String programId = String.join("-", prefixId, programDlaId);
                relationshipList.add(
                        new Relationship("uses", transactionId, programId, dlaId, programDlaId,
                                prefixId, "", "DLA", scanDate, Instant.now()));
            }
        }
    }

    private Integer saveCICSTransactionList(CICSRegion cicsRegion, String smfId, List<CICSTransaction> transactionList,
            List<Relationship> relationshipList, String prefixId, String scanDate) {
        try {
            cicsTransactionRepository.saveAll(transactionList);
            relationshipRepository.saveAll(relationshipList);
            return transactionList.size();
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveCICSTransactionList",
                    "Failed to saveAll CICSTransactionList from ZReportFile, try to save them one-by-one");
            Integer c = 0;
            for (CICSTransaction ct : transactionList) {
                try {
                    cicsTransactionRepository.save(ct);
                    Relationship rs = new Relationship("contains", cicsRegion.getId(), ct.getId(),
                            cicsRegion.getDlaId(), ct.getDlaId(), prefixId, "", "DLA", scanDate, Instant.now());
                    relationshipRepository.save(rs);
                    if (ct.getInitialProgram() != null) {
                        String programDlaId = String.format("%s-%s-%s-CICSProgram", ct.getInitialProgram(),
                                cicsRegion.getJobName(), smfId);
                        String programId = String.join("-", prefixId, programDlaId);
                        Relationship rs2 = new Relationship("uses", ct.getId(), programId, ct.getDlaId(), programDlaId,
                                prefixId, "", "DLA", scanDate, Instant.now());
                        relationshipRepository.save(rs2);
                    }
                    c += 1;
                } catch (Exception ie) {
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveCICSTransactionList",
                            "Failed to save CICSTransaction from ZReportFile, name: {}", ct.getTransactionName());
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveCICSTransactionList", ie.toString());
                }
            }
            return c;
        }
    }

    public Integer loadCICSFiles(String content, CICSRegion cicsRegion, String smfId, String scanDate) {
        List<CICSFile> fileList = new ArrayList<>();
        List<Relationship> relationshipList = new ArrayList<>();
        String prefixId = cicsRegion.getPrefixId();
        createCICSFileList(content, cicsRegion, smfId, scanDate, fileList, relationshipList, prefixId);
        return saveCICSFileList(cicsRegion, fileList, relationshipList, prefixId, scanDate);
    }

    private void createCICSFileList(String content, CICSRegion cicsRegion, String smfId, String scanDate,
            List<CICSFile> fileList, List<Relationship> relationshipList, String prefixId) {
        List<List<String>> itemList = resolveListContent(content, 2);
        for (List<String> item : itemList) {
            String name = item.get(0);
            String dlaId = String.format("%s-%s-%s-CICSFile", name, cicsRegion.getJobName(), smfId);
            String cicsFileId = stringUtils.composeUniqueId(dlaId, prefixId);
            CICSFile cicsFile = new CICSFile();
            cicsFile.setDlaId(dlaId);
            cicsFile.setPrefixId(prefixId);
            cicsFile.setId(cicsFileId);
            cicsFile.setKafkaSendDate(Instant.now());
            cicsFile.setFileName(name);
            // TODO: miss so many parameters
            if (item.size() > 1) {
                cicsFile.setDatasetName(item.get(1));
            }
            cicsFile.setScanDate(scanDate);
            fileList.add(cicsFile);

            relationshipList.add(
                    new Relationship("contains", cicsRegion.getId(), cicsFileId, cicsRegion.getDlaId(),
                            dlaId, prefixId, "", "DLA", scanDate, Instant.now()));
        }
    }

    private Integer saveCICSFileList(CICSRegion cicsRegion, List<CICSFile> fileList,
            List<Relationship> relationshipList,
            String prefixId, String scanDate) {
        try {
            cicsFileRepository.saveAll(fileList);
            relationshipRepository.saveAll(relationshipList);
            return fileList.size();
        } catch (Exception oe) {
            LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveCICSFileList",
                    "Failed to saveAll CICSFileList from ZReportFile, try to save them one-by-one");
            Integer c = 0;
            for (CICSFile cf : fileList) {
                try {
                    cicsFileRepository.save(cf);
                    Relationship rs = new Relationship("contains", cicsRegion.getId(), cf.getId(),
                            cicsRegion.getDlaId(), cf.getDlaId(), prefixId, "", "DLA", scanDate, Instant.now());
                    relationshipRepository.save(rs);
                    c += 1;
                } catch (Exception ie) {
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveCICSFileList",
                            "Failed to save CICSFile from ZReportFile, name: {}", cf.getFileName());
                    LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "saveCICSFileList", ie.toString());
                }
            }
            return c;
        }
    }

    private Map<String, String> resolveMapContent(String content) {
        String lines[] = content.split(LINE_DELIMITER);
        Map<String, String> object = new HashMap<>();
        for (String line : lines) {
            String key = "";
            String value = "";
            StringTokenizer tokenizer = new StringTokenizer(line);
            if (tokenizer.hasMoreTokens()) {
                key = tokenizer.nextToken().trim();
            }
            if (tokenizer.hasMoreTokens()) {
                value = tokenizer.nextToken().trim();
            }
            object.put(key, value);
        }
        return object;
    }

    private List<List<String>> resolveListContent(String content, int headerCount) {
        String lines[] = content.split(LINE_DELIMITER);
        List<List<String>> itemList = new ArrayList<>();
        int num = 0;
        for (String line : lines) {
            if ((num++) > headerCount) {
                List<String> paramLine = new ArrayList<>();
                StringTokenizer tokenizer = new StringTokenizer(line);
                while (tokenizer.hasMoreTokens()) {
                    paramLine.add(tokenizer.nextToken());
                }
                itemList.add(paramLine);
            }
        }
        return itemList;
    }

    // some CICSTransactions don't have Initial Program
    private List<List<String>> resolveTransactionsListContent(String content, int headerCount) {
        String lines[] = content.split(LINE_DELIMITER);
        List<List<String>> itemList = new ArrayList<>();
        int num = 0;
        int cols = 0;
        for (String line : lines) {
            List<String> paramLine = new ArrayList<>();
            StringTokenizer tokenizer = new StringTokenizer(line);
            while (tokenizer.hasMoreTokens()) {
                paramLine.add(tokenizer.nextToken());
            }

            // count the cols of the ZReportFile's content, here should be 4.
            // should be 1, not 0.
            if (num == 1) {
                cols = paramLine.size();
            }

            if ((num++) > headerCount) {
                if (paramLine.size() < cols) {
                    paramLine.add(1, ""); // for Initial Program is null
                }
                itemList.add(paramLine);
            }
        }
        return itemList;
    }

    private Object logException(String entityName, String dlaId, Exception ie) {
        LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, "logException", "Failed to save {} with DlaId: {}", entityName,
                dlaId);
        LOG.log(LogLevel.DEBUG, REPONAME, CLASSNAME, "logException", ie.toString());
        return null;
    }

}
