/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2025-2026
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.sansa.utils;

import java.util.List;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;

public class CommonUtils {
    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = CommonUtils.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    public void logMessage(String logMessage, StringBuilder infoLog, StringBuilder warnLog, StringBuilder errorLog,
            String logLevel, String methodName) {
        switch (logLevel.toLowerCase()) {
            case "warn" -> {
                LOG.log(LogLevel.WARN, REPONAME, CLASSNAME, methodName, logMessage);
                warnLog.append(logMessage).append("\n");
            }
            case "info" -> {
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, methodName, logMessage);
                infoLog.append(logMessage).append("\n");
            }
            case "error" -> {
                LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, methodName, logMessage);
                errorLog.append(logMessage).append("\n");
            }
            default -> {
                LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, methodName, logMessage); // Default to info if no valid log
                                                                                     // level is provided
                infoLog.append(logMessage).append("\n");
            }
        }
    }

    public void setValuesAtIndexes(List<String> list, int startIndex, String... values) {
        for (int i = 0; i < values.length; i++) {
            setValueAtIndex(list, startIndex + i, values[i]);
        }
    }

    public void setValueAtIndex(List<String> list, int index, String value) {
        while (list.size() <= index) {
            list.add("");
        }

        list.set(index, value == null ? "" : value);
    }

}
