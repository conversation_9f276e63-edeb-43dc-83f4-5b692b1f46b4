/** ***************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2026
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 **************************************************************** */
package com.ibm.palantir.sansa.pipeline;

import java.text.MessageFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.HashMap;

import org.springframework.stereotype.Component;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonSyntaxException;
import com.ibm.palantir.catelyn.exception.ErrorCode;
import com.ibm.palantir.catelyn.exception.MsgTemp;
import com.ibm.palantir.catelyn.exception.ServiceException;
import com.ibm.palantir.catelyn.jpa.JPAManager;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSRegionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.CICSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2DatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2StoredProcedureRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.DB2SubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSDatabaseRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSSubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.IMSTransactionRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.LPARRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQAliasQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQLocalQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQModelQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQRemoteQueueRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.MQSubsystemRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.RelationshipServiceNowRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZOSRepository;
import com.ibm.palantir.catelyn.jpa.repository.dla.ZSeriesComputerRepository;
import com.ibm.palantir.catelyn.logger.LoggerUtils;
import com.ibm.palantir.catelyn.logger.LoggerUtils.LogLevel;
import com.ibm.palantir.catelyn.pipeline.AbstractPipeline;
import com.ibm.palantir.catelyn.pipeline.PipelineConf;

@Component
public class ResendExpiredDLAItems extends AbstractPipeline {

    private static final LoggerUtils LOG = LoggerUtils.getInstance();
    private static final String CLASSNAME = ResendExpiredDLAItems.class.getSimpleName();
    private static final String REPONAME = "Sansa";

    private ZSeriesComputerRepository zSeriesComputerRepository;
    private LPARRepository lparRepository;
    private ZOSRepository zOSRepository;

    private CICSRegionRepository cicsRegionRepository;
    private CICSTransactionRepository cicsTransactionRepository;

    private DB2SubsystemRepository db2SubsystemRepository;
    private DB2DatabaseRepository db2DatabaseRepository;
    private DB2StoredProcedureRepository db2StoredProcedureRepository;

    private MQAliasQueueRepository mqAliasQueueRepository;
    private MQLocalQueueRepository mqLocalQueueRepository;
    private MQModelQueueRepository mqModelQueueRepository;
    private MQRemoteQueueRepository mqRemoteQueueRepository;
    private MQSubsystemRepository mqSubsystemRepository;

    private IMSSubsystemRepository imsSubsystemRepository;
    private IMSDatabaseRepository imsDatabaseRepository;
    private IMSTransactionRepository imsTransactionRepository;
    private RelationshipServiceNowRepository relationshipServiceNowRepository;

    private Integer resentCICount = 0;
    private Integer resentRelationshipSNOWCount = 0;

    @Override
    public String run(PipelineConf pipelineConf) throws Exception {
        String parameter = pipelineConf.getParameter();

        String invalidParamErrorCode = ErrorCode.PluginInvalidParamError.getCodeStr();
        String invalidParamErrorMsg = MessageFormat.format(MsgTemp.get(invalidParamErrorCode), "Sansa",
                "ResendExpiredDLAItems", parameter);
        String paramNullParamErrorCode = ErrorCode.PluginNullParamError.getCodeStr();
        String paramNullParamErrorMsg = MessageFormat.format(MsgTemp.get(paramNullParamErrorCode), "Sansa",
                "ResendExpiredDLAItems");

        if (parameter == null) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run", paramNullParamErrorMsg);
            throw new ServiceException(paramNullParamErrorCode, paramNullParamErrorMsg);
        }

        JsonElement jsonElement;
        Instant startDateTime;
        Instant endDateTime;

        try {
            jsonElement = JsonParser.parseString(parameter);
        } catch (JsonSyntaxException e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run", "Invalid JSON syntax in parameter: " + parameter, e);
            throw new ServiceException(invalidParamErrorCode, invalidParamErrorMsg);
        }

        if (!jsonElement.isJsonObject()) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run",
                    "The parameter format is invalid. Expected a JSON object.");
            throw new ServiceException(invalidParamErrorCode, invalidParamErrorMsg);
        }

        JsonObject jsonObject = jsonElement.getAsJsonObject();
        String startDateStr = null;

        try {
            if (jsonObject.has("startDate")) {
                startDateStr = jsonObject.get("startDate").getAsString();
                startDateTime = toStartInstant(startDateStr);
            } else {
                throw new IllegalArgumentException("Missing 'startDate' key");
            }
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run",
                    "Error parsing 'startDate': " + (startDateStr != null ? startDateStr : "null"), e);
            String errorMsg = String.format("%s (Original error: %s)", invalidParamErrorMsg,
                    e.getMessage() != null ? e.getMessage() : e.toString());
            throw new ServiceException(invalidParamErrorCode, errorMsg);
        }

        String endDateStr = null;

        try {
            if (jsonObject.has("endDate")) {
                endDateStr = jsonObject.get("endDate").getAsString();
                endDateTime = toEndInstant(endDateStr);
            } else {
                throw new IllegalArgumentException("Missing 'endDate' key");
            }
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run",
                    "Error parsing 'endDate': " + (endDateStr != null ? endDateStr : "null"), e);
            String errorMsg = String.format("%s (Original error: %s)", invalidParamErrorMsg,
                    e.getMessage() != null ? e.getMessage() : e.toString());
            throw new ServiceException(invalidParamErrorCode, errorMsg);
        }

        try {
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run",
                    String.format("The resend process started with startDateTime: %s and endDateTime: %s.",
                            startDateTime, endDateTime));
            pipelineInit();

            resendZSeriesComputer(startDateTime, endDateTime);
            resendLPAR(startDateTime, endDateTime);
            resendZOS(startDateTime, endDateTime);

            resendCICSRegion(startDateTime, endDateTime);
            resendCICSTransaction(startDateTime, endDateTime);

            resendDB2Subsystem(startDateTime, endDateTime);
            resendDB2Database(startDateTime, endDateTime);
            resendDB2StoredProcedure(startDateTime, endDateTime);

            resendMQSubsystem(startDateTime, endDateTime);
            resendMQAliasQueue(startDateTime, endDateTime);
            resendMQLocalQueue(startDateTime, endDateTime);
            resendMQModelQueue(startDateTime, endDateTime);
            resendMQRemoteQueue(startDateTime, endDateTime);

            resendIMSSubsystem(startDateTime, endDateTime);
            resendIMSDatabase(startDateTime, endDateTime);
            resendIMSTransaction(startDateTime, endDateTime);
            resendExpiredServiceNowRelationships(startDateTime, endDateTime);

        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "run", e.toString());
            String errCode = ErrorCode.PluginError.getCodeStr();
            String msg = MessageFormat.format(MsgTemp.get(errCode), "Sansa", "ResendExpiredCI",
                    parameter);
            throw new ServiceException(errCode, msg);
        }

        String resMsg = String.format(
                "The resend process completed successfully. %d expired DLA items and %d Relationship records were resent.",
                resentCICount, resentRelationshipSNOWCount);

        LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "run", resMsg);
        return resMsg;
    }

    private void pipelineInit() {
        HashMap<String, Object> repos = JPAManager.getJPAManager().getRepos();

        this.lparRepository = (LPARRepository) repos.get("lpar");
        this.zOSRepository = (ZOSRepository) repos.get("zos");
        this.zSeriesComputerRepository = (ZSeriesComputerRepository) repos.get("zSeriesComputer");

        this.cicsRegionRepository = (CICSRegionRepository) repos.get("cicsRegion");
        this.cicsTransactionRepository = (CICSTransactionRepository) repos.get("cicsTransaction");

        this.db2SubsystemRepository = (DB2SubsystemRepository) repos.get("db2Subsystem");
        this.db2DatabaseRepository = (DB2DatabaseRepository) repos.get("db2Database");
        this.db2StoredProcedureRepository = (DB2StoredProcedureRepository) repos.get("db2StoredProcedure");

        this.mqAliasQueueRepository = (MQAliasQueueRepository) repos.get("mqAliasQueue");
        this.mqLocalQueueRepository = (MQLocalQueueRepository) repos.get("mqLocalQueue");
        this.mqModelQueueRepository = (MQModelQueueRepository) repos.get("mqModelQueue");
        this.mqRemoteQueueRepository = (MQRemoteQueueRepository) repos.get("mqRemoteQueue");
        this.mqSubsystemRepository = (MQSubsystemRepository) repos.get("mqSubsystem");

        this.imsSubsystemRepository = (IMSSubsystemRepository) repos.get("imsSubsystem");
        this.imsDatabaseRepository = (IMSDatabaseRepository) repos.get("imsDatabase");
        this.imsTransactionRepository = (IMSTransactionRepository) repos.get("imsTransaction");
        this.relationshipServiceNowRepository = (RelationshipServiceNowRepository) repos.get("relationshipServiceNow");
    }

    // Resend based on the startDateTime and endDateTime
    private void resendExpiredServiceNowRelationships(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredRelationshipServiceNowCount = relationshipServiceNowRepository
                    .resendExpiredRelationshipServiceNowItems(startDateTime, endDateTime);

            resentRelationshipSNOWCount += expiredRelationshipServiceNowCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendExpiredServiceNowRelationships",
                    String.format("Resent %d expired RelationshipServiceNow items.",
                            expiredRelationshipServiceNowCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendExpiredServiceNowRelationships",
                    "Error while resending expired ServiceNowRelationships items", e);
            throw e;
        }
    }

    private void resendZSeriesComputer(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredZSeriesComputerCount = zSeriesComputerRepository.resendExpiredZSeriesComputerItems(startDateTime,
                    endDateTime);
            resentCICount += expiredZSeriesComputerCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendZSeriesComputer",
                    String.format("Resent %d expired ZSeriesComputer items.", expiredZSeriesComputerCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendZSeriesComputer",
                    "Error while resending expired ZSeriesComputer items", e);
            throw e;
        }
    }

    private void resendLPAR(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredLPARCount = lparRepository.resendExpiredLPARItems(startDateTime, endDateTime);
            resentCICount += expiredLPARCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendLPAR",
                    String.format("Resent %d expired LPAR items.", expiredLPARCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendLPAR",
                    "Error while resending expired LPAR items", e);
            throw e;
        }
    }

    private void resendZOS(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredZOSCount = zOSRepository.resendExpiredZOSItems(startDateTime, endDateTime);
            resentCICount += expiredZOSCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendZOS",
                    String.format("Resent %d expired ZOS items.", expiredZOSCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendZOS",
                    "Error while resending expired ZOS items", e);
            throw e;
        }
    }

    private void resendCICSRegion(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredCICSRegionCount = cicsRegionRepository.resendExpiredCICSRegionItems(startDateTime, endDateTime);
            resentCICount += expiredCICSRegionCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendCICSRegion",
                    String.format("Resent %d expired CICSRegion items.", expiredCICSRegionCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendCICSRegion",
                    "Error while resending expired CICSRegion items", e);
            throw e;
        }
    }

    private void resendCICSTransaction(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredCICSTransactionCount = cicsTransactionRepository.resendExpiredCICSTransactionItems(startDateTime,
                    endDateTime);
            resentCICount += expiredCICSTransactionCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendCICSTransaction",
                    String.format("Resent %d expired CICSTransaction items.", expiredCICSTransactionCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendCICSTransaction",
                    "Error while resending expired CICSTransaction items", e);
            throw e;
        }
    }

    private void resendDB2Subsystem(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredDB2SubsystemCount = db2SubsystemRepository.resendExpiredDB2SubsystemItems(startDateTime,
                    endDateTime);
            resentCICount += expiredDB2SubsystemCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendDB2Subsystem",
                    String.format("Resent %d expired DB2Subsystem.", expiredDB2SubsystemCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendDB2Subsystem",
                    "Error while resending expired DB2Subsystem items", e);
            throw e;
        }
    }

    private void resendDB2Database(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredDB2DatabaseCount = db2DatabaseRepository.resendExpiredDB2DatabaseItems(startDateTime,
                    endDateTime);
            resentCICount += expiredDB2DatabaseCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendDB2Database",
                    String.format("Resent %d expired DB2Database items.", expiredDB2DatabaseCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendDB2Database",
                    "Error while resending expired DB2Database items", e);
            throw e;
        }
    }

    private void resendDB2StoredProcedure(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredDB2StoredProcedureCount = db2StoredProcedureRepository.resendExpiredDB2StoredProcedureItems(
                    startDateTime,
                    endDateTime);
            resentCICount += expiredDB2StoredProcedureCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendDB2StoredProcedure",
                    String.format("Resent %d expired DB2StoredProcedure items.", expiredDB2StoredProcedureCount));

        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendDB2StoredProcedure",
                    "Error while resending expired DB2StoredProcedure items", e);
            throw e;
        }
    }

    private void resendMQSubsystem(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredMQSubsystemCount = mqSubsystemRepository.resendExpiredMQSubsystemItems(startDateTime,
                    endDateTime);
            resentCICount += expiredMQSubsystemCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendMQSubsystem",
                    String.format("Resent %d expired MQSubsystem items.", expiredMQSubsystemCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendMQSubsystem",
                    "Error while resending expired MQSubsystem items", e);
            throw e;
        }
    }

    private void resendMQAliasQueue(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredMQAliasQueueCount = mqAliasQueueRepository.resendExpiredMQAliasQueueItems(startDateTime,
                    endDateTime);
            resentCICount += expiredMQAliasQueueCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendMQAliasQueue",
                    String.format("Resent %d expired MQAliasQueue items.", expiredMQAliasQueueCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendMQAliasQueue",
                    "Error while resending expired MQAliasQueue items", e);
            throw e;
        }
    }

    private void resendMQLocalQueue(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredMQLocalQueueCount = mqLocalQueueRepository.resendExpiredMQLocalQueueItems(startDateTime,
                    endDateTime);
            resentCICount += expiredMQLocalQueueCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendMQLocalQueue",
                    String.format("Resent %d expired MQLocalQueue items.", expiredMQLocalQueueCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendMQLocalQueue",
                    "Error while resending expired MQLocalQueue items", e);
            throw e;
        }
    }

    private void resendMQModelQueue(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredMQModelQueueCount = mqModelQueueRepository.resendExpiredMQModelQueueItems(startDateTime,
                    endDateTime);
            resentCICount += expiredMQModelQueueCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendMQModelQueue",
                    String.format("Resent %d expired MQModelQueue items.", expiredMQModelQueueCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendMQModelQueue",
                    "Error while resending expired MQModelQueue items", e);
            throw e;
        }
    }

    private void resendMQRemoteQueue(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredMQRemoteQueueCount = mqRemoteQueueRepository.resendExpiredMQRemoteQueueItems(startDateTime,
                    endDateTime);
            resentCICount += expiredMQRemoteQueueCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendMQRemoteQueue",
                    String.format("Resent %d expired MQRemoteQueue items.", expiredMQRemoteQueueCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendMQRemoteQueue",
                    "Error while resending expired MQRemoteQueue items", e);
            throw e;
        }
    }

    private void resendIMSSubsystem(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredIMSSubsystemCount = imsSubsystemRepository.resendExpiredIMSSubsystemItems(startDateTime,
                    endDateTime);
            resentCICount += expiredIMSSubsystemCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendIMSSubsystem",
                    String.format("Resent %d expired IMSSubsystem items.", expiredIMSSubsystemCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendIMSSubsystem",
                    "Error while resending expired IMSSubsystem items", e);
            throw e;
        }
    }

    private void resendIMSDatabase(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredIMSDatabaseCount = imsDatabaseRepository.resendExpiredIMSDatabaseItems(startDateTime,
                    endDateTime);
            resentCICount += expiredIMSDatabaseCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendIMSDatabase",
                    String.format("Resent %d expired IMSDatabase items.", expiredIMSDatabaseCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendIMSDatabase",
                    "Error while resending expired IMSDatabase items", e);
            throw e;
        }
    }

    private void resendIMSTransaction(Instant startDateTime, Instant endDateTime) {
        try {
            int expiredIMSTransactionCount = imsTransactionRepository.resendExpiredIMSTransactionItems(startDateTime,
                    endDateTime);
            resentCICount += expiredIMSTransactionCount;
            LOG.log(LogLevel.INFO, REPONAME, CLASSNAME, "resendIMSTransaction",
                    String.format("Resent %d expired IMSTransaction items.", expiredIMSTransactionCount));
        } catch (Exception e) {
            LOG.log(LogLevel.ERROR, REPONAME, CLASSNAME, "resendIMSTransaction",
                    "Error while resending expired IMSTransaction items", e);
            throw e;
        }
    }

    private Instant toStartInstant(String date) {
        return LocalDate.parse(date).atStartOfDay(ZoneOffset.UTC).toInstant();
    }

    private Instant toEndInstant(String date) {
        return LocalDate.parse(date).plusDays(1).atStartOfDay(ZoneOffset.UTC).toInstant();
    }

}
