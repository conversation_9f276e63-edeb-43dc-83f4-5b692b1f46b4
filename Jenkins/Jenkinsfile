#!/usr/bin/env groovy
/*****************************************************************
 * IBM Confidential
 * OCO Source Material
 * 5698-ZAA
 * (C) Copyright IBM Corp. 2021-2026
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U. S. Copyright Office.
 *****************************************************************/

import groovy.json.JsonOutput

echo "Branch: ${env.BRANCH_NAME}"

env.BASE_PATH = "com/ibm/nazare/discovery"
env.COMPONENT = "discovery-agent"
env.VERSION = "1.4.3"
def uploadSubFolder = "$BASE_PATH/$COMPONENT"
def VERSION = "$VERSION"

def jfUrl = "https://na.artifactory.swg-devops.com/artifactory"
def jfCredentialsId = 'zoa-artifactory-token'
def jfServer = Artifactory.newServer url: "${jfUrl}", credentialsId: "${jfCredentialsId}"

def artifactory_generic_repo = 'sys-izoa-zds-generic-local'

def artifactory_maven_repo = "sys-izoa-zds-maven-local"
def artifactory_path = "com/ibm/nazare/discovery"

def now = new Date()
def buildTime = now.format("yyyyMMdd.HHmmss", TimeZone.getTimeZone('UTC'))

pipeline {
   agent {
      node {
         label 'zds-node'
      }
   }

   tools {
        jdk 'temurin-21'
   }

   // Code checkout is implicit
   stages {
      stage('Preparation') {
         steps {
           echo "Download discovery-agent-utils jar..."
           script {
             def downloadSpec = """{
               "files": [
               {  
                 "pattern": "${artifactory_maven_repo}/${artifactory_path}/discovery-agent-utils/${env.VERSION}/discovery_agent_utils_${env.VERSION}.jar",
                   "target": "lib/",
                   "flat": "true"
               }
               ]
             }"""
             jfServer.download spec: downloadSpec
           }


           sh """
           echo "Encoding files to EBCDIC for MVS Datasets and README file"
             native2ascii -reverse -encoding Cp1047 ZJOB/ZAGENT > ZJOB/ZAGENT.EBCDIC && mv ZJOB/ZAGENT.EBCDIC ZJOB/ZAGENT 
             native2ascii -reverse -encoding Cp1047 ZJOB/ZAGDEL > ZJOB/ZAGDEL.EBCDIC && mv ZJOB/ZAGDEL.EBCDIC ZJOB/ZAGDEL
             native2ascii -reverse -encoding Cp1047 ZJOB/ZAGETCT > ZJOB/ZAGETCT.EBCDIC && mv ZJOB/ZAGETCT.EBCDIC ZJOB/ZAGETCT
             native2ascii -reverse -encoding Cp1047 ZJOB/ZAGETJOB > ZJOB/ZAGETJOB.EBCDIC && mv ZJOB/ZAGETJOB.EBCDIC ZJOB/ZAGETJOB
             native2ascii -reverse -encoding Cp1047 ZJOB/ZAGREDEL > ZJOB/ZAGREDEL.EBCDIC && mv ZJOB/ZAGREDEL.EBCDIC ZJOB/ZAGREDEL
             native2ascii -reverse -encoding Cp1047 ZJOB/README.txt > ZJOB/README.txt.EBCDIC && mv ZJOB/README.txt.EBCDIC ZJOB/README.txt

             native2ascii -reverse -encoding Cp1047 script/detectExpiredDLAItems.sh > script/detectExpiredDLAItems_on_z.sh
             native2ascii -reverse -encoding Cp1047 script/getCICount.sh > script/getCICount_on_z.sh
             native2ascii -reverse -encoding Cp1047 script/getJobName.sh > script/getJobName_on_z.sh
             native2ascii -reverse -encoding Cp1047 script/discovery_agent.sh > script/discovery_agent_on_z.sh
             native2ascii -reverse -encoding Cp1047 script/resendExpiredDLAItems.sh > script/resendExpiredDLAItems_on_z.sh

            echo "Install jcl.editor.core jar..."
             mvn install:install-file -Dfile=./lib/com.ibm.systemz.jcl.editor.core-1.2.38-SNAPSHOT-jar-with-dependencies.jar -DgroupId=com.ibm.systemz -DartifactId=com.ibm.systemz.jcl.editor.core -Dversion=1.2.38-SNAPSHOT -Dpackaging=jar
           """
         }
      }

      stage('Build') {
         steps {
            sh """
            echo "Building"
            java -version
            mvn compile
            """
         }
      }

      stage('Test') {
         environment {
            scannerHome = tool 'SonarQubeScanner'
         }
         steps {
            withSonarQubeEnv('ZDSSonarQube') {
                echo "Testing & Analyzing"
                sh "mvn org.jacoco:jacoco-maven-plugin:prepare-agent -Dmaven.test.failure.ignore=false -Dproject.version=${env.VERSION} -Dbuild.time=-${buildTime} package sonar:sonar"
            }
         }
      }

      stage('Archive') {
         steps {
            script {
               if (env.BRANCH_NAME ==~ /(main|dev|PI\d+-Fix\d+)/) {
                  archiveArtifacts 'target/*.tar'

                  // VBS: Upload to ZOA artifactory, as well, since Nazare maven repo is neither the right place nor accessible to some team members
                  def zoaUploadSpec = """{
                  "files": [
                        {
                           "pattern": "target/discovery-agent-${VERSION}-mainframe.tar",
                           "target": "${artifactory_generic_repo}/DiscoveryAgent/${VERSION}/${env.BRANCH_NAME}/${buildTime}/discovery-agent-${VERSION}-mainframe.tar"
                        },
                        {
                           "pattern": "target/discovery-agent-${VERSION}-mainframe.tar",
                           "target": "${artifactory_generic_repo}/DiscoveryAgent/${VERSION}/${env.BRANCH_NAME}/latest/discovery-agent-${VERSION}-mainframe.tar"
                        },
                        {
                           "pattern": "target/discovery-agent-${VERSION}-standalone.tar",
                           "target": "${artifactory_generic_repo}/DiscoveryAgent/${VERSION}/${env.BRANCH_NAME}/${buildTime}/discovery-agent-${VERSION}-standalone.tar"
                        },
                        {
                           "pattern": "target/discovery-agent-${VERSION}-standalone.tar",
                           "target": "${artifactory_generic_repo}/DiscoveryAgent/${VERSION}/${env.BRANCH_NAME}/latest/discovery-agent-${VERSION}-standalone.tar"
                        }
                     ]
                  }"""
                  jfServer.upload spec: zoaUploadSpec
               } else {
                  echo "Archive stage is skipped for feature branches!"
               }
            }
         }
      }

      // stage ('Deploy') {
      //    steps {
      //       script {
      //          if (env.BRANCH_NAME ==~ /(master|dev|PI\d+-Fix\d+)/) {
      //             sh "mkdir -p automation"

      //             dir("automation") {
      //                git credentialsId: 'bf455b81-d487-4f9a-9d12-aab1482511d6', url: 'https://github.ibm.com/palantir/automation.git'
      //                dir("deploy") {
      //                   withCredentials([string(credentialsId: "${jfCredentialsId}", variable: 'TOKEN')]) {
      //                      sh "ansible-playbook ${env.COMPONENT}.yml -i hosts --extra-vars \"artifactory_api_key=${TOKEN}\""
      //                   }
      //                }
      //             }
      //          } else {
      //             echo "Deploy stage is skipped for feature branches!"
      //          }
      //       }
      //    }
      // }
   }

   post {
      success {
         echo "Build ${buildTime} passed"
         notifyBuild(buildTime, "SUCCESS")
         deleteDir()
      }
      failure {
         echo "Build ${buildTime} failed"
         notifyBuild(buildTime, "FAILED")
         deleteDir()
      }
   }
}

// Notify slack for branch builds
def notifyBuild(String buildTime, String buildStatus = 'STARTED')
{
    // build status of null means successful
    buildStatus = buildStatus ?: 'SUCCESS'

    // Default values
    def colorName = 'RED'
    def colorCode = '#FF0000'
    def gitCommitMessage = sh(returnStdout: true, script: 'git log -1 --pretty=%B').trim()

    def subject = "${env.JOB_NAME} - Build #${env.BUILD_NUMBER} : ${buildStatus}"
    def innerText = "Build started at: ${buildTime}"

    if (buildStatus == 'STARTED')
    {
        colorName = 'YELLOW'
        colorCode = '#FFFF00'
    }
    else if (buildStatus == 'SUCCESS')
    {
        colorName = 'GREEN'
        colorCode = '#00FF00'
    }
    else
    {
        colorName = 'RED'
        colorCode = '#FF0000'
    }

   def message = JsonOutput.toJson([
      attachments: [[
         pretext: 'Nazare Discovery CI/CD',
         title: "${subject}",
         title_link: "${env.BUILD_URL}",
         text: "${innerText}",
         fields: [[
               title: 'Component',
               value: "${env.COMPONENT}",
               short: true
            ],
            [
               title: 'Branch',
               value: "${env.BRANCH_NAME}",
               short: true
            ]],
         color: "${colorCode}",
         footer: 'Nazare Devops',
         footer_icon: 'https://platform.slack-edge.com/img/default_application_icon.png'
      ]]
   ])

   String slackUrl = "*******************************************************************************"
   sh("curl -X POST -H 'Content-type: application/json' --data \'${message}\' ${slackUrl}")
}
