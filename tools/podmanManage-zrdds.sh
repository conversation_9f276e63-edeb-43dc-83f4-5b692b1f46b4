#!/bin/bash
######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-LDA (C) Copyright IBM Corp. 2024, 2025
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

#--------------------------------------------------------------------------
# Name:            podmanManage-zrdds.sh
#
# Description:     IBM Z Data Analytics Platform management
#
# Product version: __PRODVER__
# Build ID:        __BUILDID__
# Build date:      __BUILDDATE__
#
# Purpose:         <PERSON><PERSON><PERSON> used to manage the OCI containers of the IBM
#                  Z Data Analytics Platform feature
#
# Syntax:          podmanManage-zrdds.sh
#
#
# MAINTENANCE:
#
#+------+-------+--------+---+----------------------------------------
#|FLAG  |APAR#  |  DATE  |WHO| DESCRIPTION
#|      |PTF#   |        |   | PMR
#|------+-------+--------+---+----------------------------------------
#|      |       |        |   |
#+------+-------+--------+---+----------------------------------------
#
#---------------------------------------------------------------------

NAMESPACE_ZRDDS=zrdds

ZRDDS_SETLOGLEVEL=""
ZRDDS_OCI_FEATURE_LABEL="IBM Z AIOps - Z Resource Discovery Data Service"

if [ "${DNS_TYPE}" == "legacy" ]
then
  ZRDDS_CONTAINER_PREFIX=""
elif [ "${DNS_TYPE}" == "modern" ]
then
  ZRDDS_CONTAINER_PREFIX=${NAMESPACE_ZRDDS}-
fi

set -a
. ${BASEDIR}/${NEW_CONFIG}
. ${BASEDIR}/${IBM_CONFIG}
. ${BASEDIR}/bin/utils/zrdds-common.sh
set +a

if [ "${ARCH}" == "x86_64" ]
then
  ZRDDS_ARCH=amd64
else
  ZRDDS_ARCH=${ARCH}
fi

zrddskafkabridgeUp() {
  if(isContainerRunning zrdds-kafkabridge)
  then
    echo "Container 'zrdds-kafkabridge' is already running."
  else
    if [ "${DISCOVERY_SCENARIO}" == "ALL" ]
    then
      echo "Will start 'zrdds-kafkabridge' service."
      ${OCI_AGENT} run --restart=on-failure \
        --name zrdds-kafkabridge -d \
        --security-opt no-new-privileges \
        ${LOGOPTS[@]} \
        -h ${ZRDDS_KB_HOST} --network ${COMPOSE_PROJECT_NAME}_zaiops \
        ibm-zaiops/zrdds-kafkabridge:${ZRDDS_TAG}-${ZRDDS_ARCH} > /dev/null
      sleep 5
    fi
  fi
}

zrddscoreUp() {
  if(isContainerRunning zrdds-core)
  then
    echo "Container 'zrdds-core' is already running."
  else
    ZRDDS_PGSQL_READY=FALSE
    count=0
    echo -n "Waiting for ZRDDS PostgreSQL service to be ready "
    while :
    do
      echo -n .
      ${OCI_AGENT} ps -a -f health=healthy 2>/dev/null | grep -q zrdds-postgresql
      if [ $? -eq 0 ]
      then
        echo -n " "
        echo "successful after ${count} seconds."
        ZRDDS_PGSQL_READY="TRUE"
        break
      elif [ ${count} -ge ${SERVICE_TIMEOUT} ]
      then
        echo -n " "
        echo "ZRDDS PostgreSQL service still not ready after ${count} seconds; giving up."
        break
      else
        sleep 5
        count=$(( ${count} + 5 ))
      fi
    done
    ZRDDS_ORIENTDB_READY=FALSE
    count=0
    echo -n "Waiting for ZRDDS OrientDB service to be ready "
    while :
    do
      echo -n .
      ${OCI_AGENT} ps -a -f health=healthy 2>/dev/null | grep -q zrdds-orientdb
      if [ $? -eq 0 ]
      then
        echo -n " "
        echo "successful after ${count} seconds."
        ZRDDS_ORIENTDB_READY="TRUE"
        break
      elif [ ${count} -ge ${SERVICE_TIMEOUT} ]
      then
        echo -n " "
        echo "ZRDDS OrientDB service still not ready after ${count} seconds; giving up."
        break
      else
        sleep 5
        count=$(( ${count} + 5 ))
      fi
    done
    if [ "${ZRDDS_PGSQL_READY}" == "TRUE" ] && [ "${ZRDDS_ORIENTDB_READY}" == "TRUE" ]
    then
      echo "Will start 'zrdds-core' service."
      ${OCI_AGENT} run --restart=always \
        --name zrdds-core -d \
        --security-opt no-new-privileges \
        ${LOGOPTS[@]} \
        -e SPRING_DATASOURCE_PRIMARY_URL="***************************************/${PGSQL_DB}?currentSchema=public" \
        -e PGSQL_TOKEN=${PGSQL_TOKEN} \
        -e LOGGING_LEVEL_ROOT=${ZRDDS_CORE_LOGGING_LEVEL:-INFO} \
        -e SERVER_SSL_ENABLED=false \
        -v ${COMPOSE_PROJECT_NAME}_zrdds_data:/app/zrdds-core/upload-dir \
        -v ${COMPOSE_PROJECT_NAME}_zrdds_config:/app/zrdds-core/external-config \
        -h zrdds-core --network ${COMPOSE_PROJECT_NAME}_zaiops \
        ibm-zaiops/zrdds-core:${ZRDDS_TAG}-${ZRDDS_ARCH} > /dev/null
      sleep 5
    fi
  fi
}

zrddsapiUp() {
  if(isContainerRunning zrdds-api)
  then
    echo "Container 'zrdds-api' is already running."
  else
    ZRDDS_CORE_READY=FALSE
    count=0
    echo -n "Waiting for ZRDDS core service to be ready "
    while :
    do
      echo -n .
      ${OCI_AGENT} ps -a -f health=healthy 2>/dev/null | grep -q zrdds-core
      if [ $? -eq 0 ]
      then
        echo -n " "
        echo "successful after ${count} seconds."
        ZRDDS_CORE_READY="TRUE"
        break
      elif [ ${count} -ge ${SERVICE_TIMEOUT} ]
      then
        echo -n " "
        echo "ZRDDS core service still not ready after ${count} seconds; giving up."
        break
      else
        sleep 5
        count=$(( ${count} + 5 ))
      fi
    done
    ZRDDS_ORIENTDB_READY=FALSE
    count=0
    echo -n "Waiting for ZRDDS OrientDB service to be ready "
    while :
    do
      echo -n .
      ${OCI_AGENT} ps -a -f health=healthy 2>/dev/null | grep -q zrdds-orientdb
      if [ $? -eq 0 ]
      then
        echo -n " "
        echo "successful after ${count} seconds."
        ZRDDS_ORIENTDB_READY="TRUE"
        break
      elif [ ${count} -ge ${SERVICE_TIMEOUT} ]
      then
        echo -n " "
        echo "ZRDDS OrientDB service still not ready after ${count} seconds; giving up."
        break
      else
        sleep 5
        count=$(( ${count} + 5 ))
      fi
    done
    if [ "${ZRDDS_CORE_READY}" == "TRUE" ] && [ "${ZRDDS_ORIENTDB_READY}" == "TRUE" ]
    then
      echo "Will start 'zrdds-api' service."
      ${OCI_AGENT} run --restart=always \
        --name zrdds-api -d \
        --security-opt no-new-privileges \
        ${LOGOPTS[@]} \
        -e ORIENT_URL=remote:zrdds-orientdb/Demo \
        -e ORIENT_TOKEN=${ORIENT_TOKEN} \
        -e ZRDDS_CORE_URL=http://zrdds-core:8080/api/v1 \
        -e KEYCLOAK_SERVER=http://auth:8080/auth/ \
        -e AUTHENTICATION_MODE=keycloak \
        -e KEYCLOAK_JWKS_URI=http://auth:8080/auth/realms/IzoaKeycloak/protocol/openid-connect/certs \
        -e DBZ_TOKEN=${PGSQL_TOKEN} \
        -e LOGGING_LEVEL_ROOT=${ZRDDS_API_LOGGING_LEVEL:-INFO} \
        -e SERVER_SSL_ENABLED=false \
        -v ${COMPOSE_PROJECT_NAME}_zrdds_data:/app/zrdds-api/upload-dir \
        -h zrdds-api --network ${COMPOSE_PROJECT_NAME}_zaiops \
        ibm-zaiops/zrdds-api:${ZRDDS_TAG}-${ZRDDS_ARCH} > /dev/null
      sleep 5
    fi
  fi
}

zrddspostgresqlUp() {
  if(isContainerRunning zrdds-postgresql)
  then
    echo "Container 'zrdds-postgresql' is already running."
  else
    echo "Will start 'zrdds-postgresql' service."
    ${OCI_AGENT} run --restart=always \
      --name zrdds-postgresql -d \
      --security-opt no-new-privileges \
      ${LOGOPTS[@]} \
      -e PGSQL_TOKEN=${PGSQL_TOKEN} \
      -e PGSQL_ADMIN_TOKEN=${PGSQL_ADMIN_TOKEN} \
      -e POSTGRESQL_DATABASE=${PGSQL_DB} \
      -e ENABLE_REPLICATION="true" \
      -v ${COMPOSE_PROJECT_NAME}_pgsql_data:/var/lib/pgsql/data \
      -h zrdds-postgresql --network ${COMPOSE_PROJECT_NAME}_zaiops \
      ibm-zaiops/zrdds-postgresql:${ZRDDS_TAG}-${ZRDDS_ARCH} > /dev/null
    sleep 5
  fi
}

zrddsorientdbUp() {
  if(isContainerRunning zrdds-orientdb)
  then
    echo "Container 'zrdds-orientdb' is already running."
  else
    echo "Will start 'zrdds-orientdb' service."
    ${OCI_AGENT} run --restart=always \
      --name zrdds-orientdb -d \
      --security-opt no-new-privileges \
      ${LOGOPTS[@]} \
      -e ORIENT_TOKEN=${ORIENT_TOKEN} \
      -e ORIENTDB_OPTS_MEMORY="${ORIENTDB_HEAP_SIZE}" \
      -v ${COMPOSE_PROJECT_NAME}_orientdb_data:/orientdb/databases \
      -h zrdds-orientdb --network ${COMPOSE_PROJECT_NAME}_zaiops \
      ibm-zaiops/zrdds-orientdb:${ZRDDS_TAG}-${ZRDDS_ARCH} > /dev/null
    sleep 5
  fi
}

zrddskafkaconnectUp() {
  if(isContainerRunning zrdds-kafkaconnect)
  then
    echo "Container 'zrdds-kafkaconnect' is already running."
  else
    if [ "${DISCOVERY_SCENARIO}" == "ALL" ]
    then
      ZRDDS_CORE_READY=FALSE
      count=0
      echo -n "Waiting for ZRDDS core service to be ready "
      while :
      do
        echo -n .
        ${OCI_AGENT} ps -a -f health=healthy 2>/dev/null | grep -q zrdds-core
        if [ $? -eq 0 ]
        then
          echo -n " "
          echo "successful after ${count} seconds."
          ZRDDS_CORE_READY="TRUE"
          break
        elif [ ${count} -ge ${SERVICE_TIMEOUT} ]
        then
          echo -n " "
          echo "ZRDDS core service still not ready after ${count} seconds; giving up."
          break
        else
          sleep 5
          count=$(( ${count} + 5 ))
        fi
      done
      ZRDDS_API_READY=FALSE
      count=0
      echo -n "Waiting for ZRDDS core service to be ready "
      while :
      do
        echo -n .
        ${OCI_AGENT} ps -a -f health=healthy 2>/dev/null | grep -q zrdds-api
        if [ $? -eq 0 ]
        then
          echo -n " "
          echo "successful after ${count} seconds."
          ZRDDS_API_READY="TRUE"
          break
        elif [ ${count} -ge ${SERVICE_TIMEOUT} ]
        then
          echo -n " "
          echo "ZRDDS core service still not ready after ${count} seconds; giving up."
          break
        else
          sleep 5
          count=$(( ${count} + 5 ))
        fi
      done
      if [ "${ZRDDS_CORE_READY}" == "TRUE" ] && [ "${ZRDDS_API_READY}" == "TRUE" ]
      then
        echo "Will start 'zrdds-kafkaconnect' service."
        ${OCI_AGENT} run --restart=always \
          --name zrdds-kafkaconnect -d \
          --security-opt no-new-privileges \
          ${LOGOPTS[@]} \
          -e KAFKA_BOOTSTRAP_SERVER_HOST=kafkabroker \
          -e KAFKA_BOOTSTRAP_SERVER_PORT=19092 \
          -e PGSQL_ADMIN_TOKEN=${PGSQL_ADMIN_TOKEN} \
          -e PGSQL_TOKEN=${PGSQL_TOKEN} \
          -e POSTGRES_DATABASE_HOSTNAME=zrdds-postgresql \
          -e POSTGRES_DATABASE_PORT=5432 \
          -v ${COMPOSE_PROJECT_NAME}_kafka_connect_data:/opt/kafka/data \
          -h zrdds-kafkaconnect --network ${COMPOSE_PROJECT_NAME}_zaiops \
          ibm-zaiops/zrdds-kafkaconnect:${ZRDDS_TAG}-${ZRDDS_ARCH} > /dev/null
        sleep 5
      fi
    fi
  fi
}


## MAIN
ARG=${1}
case "${ARG}" in
  "help" )
    helpZRDDS
    ;;
  "serviceToContainer" )
    serviceToContainerZRDDS ${2}
    ;;
esac
