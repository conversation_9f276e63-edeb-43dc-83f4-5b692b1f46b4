services:
  zrdds-postgresql:
    image: icr.io/zoa-oci/zrdds-postgresql:${ZRDDS_TAG}-amd64
    restart: always
    #ports:
    #- ${PGSQL_PORT}:5432
    volumes:
      - pgsql_data:/var/lib/pgsql/data
    environment:
      PGSQL_TOKEN: ${PGSQL_TOKEN}
      PGSQL_ADMIN_TOKEN: ${PGSQL_ADMIN_TOKEN}
      POSTGRESQL_DATABASE: ${PGSQL_DB}
      ENABLE_REPLICATION: "true"
    hostname: zrdds-postgresql
    container_name: zrdds-postgresql
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    logging:
      driver: ${LOGGING_DRIVER:-json-file}
      options:
        mode: ${LOGGING_MODE:-non-blocking}
    networks:
      - zaiops

  zrdds-orientdb:
    image: icr.io/zoa-oci/zrdds-orientdb:${ZRD<PERSON>_TAG}-amd64
    restart: always
    #ports:
    #  - 2424:2424
    #  - 2480:2480
    volumes:
      - orientdb_data:/orientdb/databases
    environment:
      ORIENT_TOKEN: ${ORIENT_TOKEN}
      ORIENTDB_OPTS_MEMORY: "${ORIENTDB_HEAP_SIZE}"
    hostname: zrdds-orientdb
    container_name: zrdds-orientdb
    logging:
      driver: ${LOGGING_DRIVER:-json-file}
      options:
        mode: ${LOGGING_MODE:-non-blocking}
    networks:
      - zaiops

  zrdds-core:
    image: icr.io/zoa-oci/zrdds-core:${ZRDDS_TAG}-amd64
    restart: always
    #ports:
    #- ${COREAPI_PORT}:8080
    depends_on:
      zrdds-postgresql:
        condition: service_healthy
      zrdds-orientdb:
        condition: service_healthy
    volumes:
      - zrdds_data:/app/zrdds-core/upload-dir
      - zrdds_config:/app/zrdds-core/external-config
    environment:
      SPRING_DATASOURCE_PRIMARY_URL: ***************************************/${PGSQL_DB}?currentSchema=public
      PGSQL_TOKEN: ${PGSQL_TOKEN}
      LOGGING_LEVEL_ROOT: ${ZRDDS_CORE_LOGGING_LEVEL:-INFO}
      SERVER_SSL_ENABLED: false
    hostname: zrdds-core
    container_name: zrdds-core
    logging:
      driver: ${LOGGING_DRIVER:-json-file}
      options:
        mode: ${LOGGING_MODE:-non-blocking}
    networks:
      - zaiops

  zrdds-api:
    image: icr.io/zoa-oci/zrdds-api:${ZRDDS_TAG}-amd64
    restart: always
    #ports:
    #- ${ZRDDS_PORT}:8888
    depends_on:
      zrdds-orientdb:
        condition: service_healthy
      zrdds-core:
        condition: service_healthy
    volumes:
      - zrdds_data:/app/zrdds-api/upload-dir
    environment:
      ORIENT_URL: remote:zrdds-orientdb/Demo
      ORIENT_TOKEN: ${ORIENT_TOKEN}
      ZRDDS_CORE_URL: http://zrdds-core:8080/api/v1
      KEYCLOAK_SERVER: http://auth:8080/auth/
      #KEYCLOAK_SECRET: d56b8810-b84d-416f-8554-eb3db9a59760
      AUTHENTICATION_MODE: keycloak
      KEYCLOAK_JWKS_URI: http://auth:8080/auth/realms/IzoaKeycloak/protocol/openid-connect/certs
      DBZ_TOKEN: ${PGSQL_TOKEN}
      LOGGING_LEVEL_ROOT: ${ZRDDS_API_LOGGING_LEVEL:-INFO}
      SERVER_SSL_ENABLED: false
    hostname: zrdds-api
    container_name: zrdds-api
    logging:
      driver: ${LOGGING_DRIVER:-json-file}
      options:
        mode: ${LOGGING_MODE:-non-blocking}
    networks:
      - zaiops

  zrdds-kafkaconnect:
    image: icr.io/zoa-oci/zrdds-kafkaconnect:${ZRDDS_TAG}-amd64
    restart: always
    depends_on:
      zrdds-core:
        condition: service_healthy
      zrdds-api:
        condition: service_healthy
    volumes:
      - kafka_connect_data:/opt/kafka/data
    environment:
      KAFKA_BOOTSTRAP_SERVER_HOST: kafkabroker
      KAFKA_BOOTSTRAP_SERVER_PORT: 19092
      PGSQL_ADMIN_TOKEN: ${PGSQL_ADMIN_TOKEN}
      PGSQL_TOKEN: ${PGSQL_TOKEN}
      POSTGRES_DATABASE_HOSTNAME: zrdds-postgresql
      POSTGRES_DATABASE_PORT: 5432
    hostname: zrdds-kafkaconnect
    container_name: zrdds-kafkaconnect
    logging:
      driver: ${LOGGING_DRIVER:-json-file}
      options:
        mode: ${LOGGING_MODE:-non-blocking}
    networks:
      - zaiops
    profiles:
      - ALL

  zrdds-kafkabridge:
    image: icr.io/zoa-oci/zrdds-kafkabridge:${ZRDDS_TAG}-amd64
    container_name: zrdds-kafkabridge
    hostname: ${ZRDDS_KB_HOST}
    restart: always
    logging:
      driver: ${LOGGING_DRIVER:-json-file}
      options:
        mode: ${LOGGING_MODE:-non-blocking}
    networks:
      - zaiops
    profiles:
      - ALL

networks:
  zaiops:
    driver: bridge

volumes:
  orientdb_data:
  pgsql_data:
  zrdds_data:
  zrdds_config:
  kafka_connect_data:
