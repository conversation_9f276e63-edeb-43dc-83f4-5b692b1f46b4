#!/bin/sh

######################################################### {COPYRIGHT-TOP} ###
# Licensed Materials - Property of IBM
# 5698-ZAA (C) Copyright IBM Corp. 2026
# All rights reserved.
# US Government Users Restricted Rights - Use, duplication or
# disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
######################################################### {COPYRIGHT-END} ###

SCRIPTDIR="$( cd "$( dirname "$0" )" && pwd )"

getRealScript() {
  INPUT_FILE=${1}
  FILETEST=`file -h ${INPUT_FILE}`
  echo ${FILETEST} | grep -q "symbolic link"
  if [ $? = 0 ]
  then
    echo ${FILETEST} | awk '{ print $5 }'
  else
    echo ${INPUT_FILE}
  fi
}

REALSCRIPT=`getRealScript ${0}`

SCRIPTDIR="$( cd "$( dirname "$REALSCRIPT" )" && pwd )"
AGENTDIR="$( cd "${SCRIPTDIR}/.." && pwd )"
LIBDIR="$( cd "${AGENTDIR}/lib" && pwd )"
INTERNALCONFIGDIR="$( cd "${AGENTDIR}/internal_config" && pwd )"
ORIG_DIR="$( pwd )"
LOG_CONFIG_PATH=${INTERNALCONFIGDIR}/log.xml

#RETRY_SETTING='-Drest.max_per_minute=20 -Dretry.sleep_in_seconds=6 -Dretry.number=2'
RETRY_SETTING=''
COMM_CONFIG="${ZDA_COMM_CONFIG}"
OTHER_CONFIG="${ZDA_OTHER_CONFIG}"

if [ $# -gt 0 ]
then
  COMM_CONFIG=${1}
fi
if [ $# -gt 1 ]
then
  shift
  OTHER_CONFIG="$*"
fi

if [ ! "${COMM_CONFIG}x" = "x" ]
then
  COMM_CONFIG=${AGENTDIR}/${COMM_CONFIG}
fi
if [ ! "${OTHER_CONFIG}x" = "x" ]
then
  OTHER_CONFIG=${OTHER_CONFIG}
fi

cd ${AGENTDIR}
echo ""
echo "Running Resend Expired DLA Items with the following parameters:"
echo "  PATH:                              ${PATH}"
echo "  Agent Directory:                   ${AGENTDIR}"
echo "  Original Directory:                ${ORIG_DIR}"
echo "  Current Directory:                 $( pwd )"
echo "  Log configuration path:            ${LOG_CONFIG_PATH}"
echo "  Retry settings:                    ${RETRY_SETTING:-[ not set ]}"
echo "  Communications configuration file: ${COMM_CONFIG:-[ not set ]}"
echo "  Other Parameters:                  ${OTHER_CONFIG}"
echo ""

java ${RETRY_SETTING} \
  -Dlogback.configurationFile=${LOG_CONFIG_PATH} \
  -jar ${LIBDIR}/discovery_agent_utils_1.4.3.jar \
  ${COMM_CONFIG} \
  resendExpiredDLAItems \
  ${OTHER_CONFIG}