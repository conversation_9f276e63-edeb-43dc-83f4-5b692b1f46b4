# WARNING: This is not for user modification

config:
  jcl_batch_size: ${USER_CONFIG.JCL_BATCH_SIZE}
  idml_file_batch: ${USER_CONFIG.IDML_FILE_BATCH}
  expired_interval: ${USER_CONFIG.EXPIRED_INTERVAL}
  getjobname_line_prefix: ${USER_CONFIG.GETJOBNAME_LINE_PREFIX}
  job_line_char_limit: ${USER_CONFIG.JOB_LINE_CHAR_LIMIT}
  copy_into_orient_mode: ${USER_CONFIG.COPY_INTO_ORIENT_MODE}


persist:
  - type: API_JCL
    version: Data version
    url: https://${USER_CONFIG.ZRDDS_HOST_NAME}:${USER_CONFIG.GATEWAY_PORT}/zrdds/api/v1/process/APIDataTransfer
    password: ${USER_CONFIG.PASSWORD}
    option:
      updateSystemRelationship: true
      generateUrl: https://${USER_CONFIG.ZRDDS_HOST_NAME}:${USER_CONFIG.GATEWAY_PORT}/zrdds/api/v1/process/JCLOperationDataGenerate
      keycloakTokenUri: https://${USER_CONFIG.ZRDDS_HOST_NAME}:${USER_CONFIG.GATEWAY_PORT}/auth/realms/IzoaKeycloak/protocol/openid-connect/token
      keycloakClientGrantType: password
      keycloakClientId: ${USER_CONFIG.KEYCLOAK_CLIENT_ID}
      keycloakClientUsername: ${USER_CONFIG.KEYCLOAK_CLIENT_USERNAME}
      keycloakClientPassword: ${USER_CONFIG.KEYCLOAK_CLIENT_PASSWORD}
  - type: API_DLA
    version: Data version
    url: https://${USER_CONFIG.ZRDDS_HOST_NAME}:${USER_CONFIG.GATEWAY_PORT}/zrdds/api/v1/file
    password: ${USER_CONFIG.PASSWORD}
    option:
      updateSystemRelationship: https://${USER_CONFIG.ZRDDS_HOST_NAME}:${USER_CONFIG.GATEWAY_PORT}/zrdds/api/v1/process/APIDataTransfer
      populateDLAUrl: https://${USER_CONFIG.ZRDDS_HOST_NAME}:${USER_CONFIG.GATEWAY_PORT}/zrdds/api/v1/process/PopulateDLA
      copyIntoOrientUrl: https://${USER_CONFIG.ZRDDS_HOST_NAME}:${USER_CONFIG.GATEWAY_PORT}/zrdds/api/v1/process/CopyIntoOrient
      keycloakTokenUri: https://${USER_CONFIG.ZRDDS_HOST_NAME}:${USER_CONFIG.GATEWAY_PORT}/auth/realms/IzoaKeycloak/protocol/openid-connect/token
      keycloakClientGrantType: password
      keycloakClientId: ${USER_CONFIG.KEYCLOAK_CLIENT_ID}
      keycloakClientUsername: ${USER_CONFIG.KEYCLOAK_CLIENT_USERNAME}
      keycloakClientPassword: ${USER_CONFIG.KEYCLOAK_CLIENT_PASSWORD}
  - type: API_UTILS 
    version: Data version
    url: https://${USER_CONFIG.ZRDDS_HOST_NAME}:${USER_CONFIG.GATEWAY_PORT}/zrdds/api/v1
    password: ${USER_CONFIG.PASSWORD}
    option:
      detectExpiredDLAItemsUrl: https://${USER_CONFIG.ZRDDS_HOST_NAME}:${USER_CONFIG.GATEWAY_PORT}/zrdds/api/v1/process/DetectExpiredDLAItems
      resendExpiredDLAItemsUrl: https://${USER_CONFIG.ZRDDS_HOST_NAME}:${USER_CONFIG.GATEWAY_PORT}/zrdds/api/v1/process/ResendExpiredDLAItems
      keycloakTokenUri: https://${USER_CONFIG.ZRDDS_HOST_NAME}:${USER_CONFIG.GATEWAY_PORT}/auth/realms/IzoaKeycloak/protocol/openid-connect/token
      keycloakClientGrantType: password
      keycloakClientId: ${USER_CONFIG.KEYCLOAK_CLIENT_ID}
      keycloakClientUsername: ${USER_CONFIG.KEYCLOAK_CLIENT_USERNAME}
      keycloakClientPassword: ${USER_CONFIG.KEYCLOAK_CLIENT_PASSWORD}