Product:  Discovery Agent
Version:  1.4.3
Contents:
=========
1.0 General description
2.0 Instruction



1.0 General description
=================
This package is the standalone agent for Discovery . This job is used to call the Discovery Agent script under the USS.

2.0 Prerequisites
=================
Java 21, please refer to the JCL file for any additional prerequisites

3.0 Instruction
=================

3.1 Configuration of the Z Discovery Agent to process IDML Books
-----------------------------------------------------------------
The IBM zDiscovery Agent supports multiple functions.   One of the primary function is to process IDML books or JCL.
Other functions including getting a count of CIs known to the zDiscovery Server, or marking CI as expired.

AFter untar the *.tar package, please perform the following:
1. Update the tagging of all the *.YML and *.XML files to "ASCII" by:
      cd <zDiscovery Agent Root>
      chtag -t -c ISO8859-1 config/*
      chtag -t -c ISO8859-1 internal_config/*

   Note: You can verify the tagging result using the following commands:
                ls -Tla config/
            The following are the expected output (notice the "t ISO8859-1   T=on" indicate that the file is tagged as ASCII)
                drwxrwxr-x   2 <USER>    <GROUP>       8192 Oct  5 10:42 ./
                drwxrwxrwx   8 <USER>    <GROUP>       8192 Oct  9 14:50 ../
                t ISO8859-1   T=on  -rw-r--r--   1 <USER>    <GROUP>        376 Oct  5 10:42 CONFIG.YML
                t ISO8859-1   T=on  -rw-r--r--   1 <USER>    <GROUP>        376 Oct  5 10:42 FIlE_DYNAMIC_JCL.YML
                t ISO8859-1   T=on  -rw-r--r--   1 <USER>    <GROUP>        364 Oct  5 10:42 FILE_IDML.YML
                t ISO8859-1   T=on  -rw-r--r--   1 <USER>    <GROUP>        380 Oct  5 10:42 FILE_ROUTECARD.YML
                t ISO8859-1   T=on  -rw-r--r--   1 <USER>    <GROUP>        374 Oct  5 10:42 FILE_STATIC_JCL.YML
        Please repeat the same for the internal_config directory.

2. Make a copy of the <agent installation root>/config directory as backup.

3. Update the config/CONFIG.YML with appropriate value.
      This files contains base parameter required to run the Agent and communicate with the Server.

4. Depending on the function, you might need additional configuration.
   For the IDML book or JCL usecase, please modify one of the
          config/FILE_IDML.YML, config/FILE_STATIC_JCL.YML, config/FILE_ROUTECARD.YML or config/DYNAMIC_JCL.YML
   with the data that you intended to process.

   The FILE_*.YML file contains the following parameters:
        <SYSPLEX_NAME>
                     Specifies the SYSPLEX name of JCL Static file location.
        <DATASET_NAME>
                     Specifies the DATASET name of JCL static file location, dynamic file location, or Route Card file location.
        <MEMBER_NAME>
                     Specifies the name of the JCL static file to scan, dynamic file to load, and route card file to convert.

                        Notes: Only for MEMBER_NAME in jcl.static.files. You can use wildcard characters `?` and
                        `*` to match the name and specify multiple files. `?` matches one character, and `*` matches
                        zero or more characters. For example, `Sam*.jcl` matches all the files that start with Sam, and
                        end with .jcl

    For other functions, please refer to IBM documentation for the parameters.


3.2 Copy the JCL to the MVS jcl lib
-------------------------------------
After you uncompress the package, there are multiple JCL files (e.g. ZAGENT, ZAGDEL, ZAGETCT, ZAGETJOB, ZAGREDEL) under the /#path/ZJOB,
you can copy them to the exist ing job lib or a newly created job lib on z/OS using the JCL below:

//OCOPY JOB ,REGION=0M,MSGCLASS=C,MSGLEVEL=(1,1),NOTIFY=&SYSUID
//********************************************************************
//* Copy HFS output files to SYSOUT, since BPXfile* can only write
//* STDOUT and STDERR to HFS files.
//********************************************************************
//COPYSTEP EXEC PGM=IKJEFT01
//INHFS DD PATH='#path/ZJOB/ZAGENT',
// PATHOPTS=(ORDONLY)
//OUTMVS DD DSN=#jcldataset(ZAGENT),DISP=SHR
//SYSTSPRT DD SYSOUT=*
//SYSTSIN DD *
OCOPY INDD(INHFS) OUTDD(OUTMVS) TEXT CONVERT(YES) PATHOPTS(USE)
/*
//
#path
    Specifies the absolute path under USS where the JCL file is.
#jcldataset
    Specifies the JCL library on MVS where you want to put the JCL. You can use an existing JCL library, or create a new JCL library before you copy this JCL file.
Run script

Go to the JCL where you copied, change some parameters according to the description in JCL, and then submit it.
