//ZAGETJOB  JOB ,REGION=0M,MSGCLASS=C,MSGLEVEL=(1,1),NOTIFY=&SYSUID
//*********************************************************************
//* Parameter customization:                                        ***
//* 1. Set SCPATH to the absolute path of the following script:     ***
//*            getJobName.sh                                        ***
//*    If the path to the scripts is very long, then create a short ***
//*    symbolic link to the script and use that link as the value   ***
//*    of SCPATH.                                                   ***
//*    Example (Discovery Agent is installed in                     ***
//*    /usr/lpp/IBM/ZDiscoveryAgent):                               ***
//*      mkdir -p /usr/share/bin                                    ***
//*      ln -s /usr/lpp/IBM/ZDiscoveryAgent/script/                 ***
//*            getJobName.sh /usr/share/bin/zgj.sh                  ***
//*    In this example, you can use the following                   ***
//*    as the value of SCPATH:                                      ***
//*            /usr/share/bin/zgj.sh                                ***
//* 2. Set JAVAHOME to the directory in which Java 21 is installed. ***
//* 3. Set COMMPARM to the name of the file that contains the       ***
//*    various settings of the agent.  It includes communication    ***
//*    settings between Discovery Agent and Z Resource Discovery    *** 
//*    Data Service. Typically, this should have a value of:        ***
//*             CONFIG/CONFIG.YML                                   ***
//* 4. Set OTHPARM to the additional parameters for the script.     ***
//*    Please refer to IBM documentation for supported parameters.  ***
//* 5. Make sure that execute permissions are set on the scripts:   ***
//*      Example,                                                   ***
//*        chmod 755 <full_path_to>/discovery_agent.sh              ***
//*    , and that the user ID submitting this JCL has read and      ***
//*    write access to the directory in which the Discovery Agent   ***
//*    is installed.                                                ***
//*********************************************************************
//* ***            UPDATE SETTINGS IN THIS SECTION                  ***
//*********************************************************************
//*
// EXPORT SYMLIST=*
// SET SCPATH=''
// SET JAVAHOME=''
// SET COMMPARM=''
// SET OTHPARM=''
// SET OUTCLS=*
//*
//*********************************************************************
//* ***                  DO NOT MODIFY BELOW                        ***
//* Run Java under a UNIX System Service shell
//*********************************************************************
//STEP1 EXEC PGM=BPXBATCH
//STDOUT   DD SYSOUT=&OUTCLS
//STDERR   DD SYSOUT=&OUTCLS
//STDPARM DD *,SYMBOLS=EXECSYS
PGM &SCPATH
//STDENV DD *,SYMBOLS=EXECSYS
JAVA_HOME=&JAVAHOME
JAVA_TOOL_OPTIONS="-Dfile.encoding=IBM-1047"
ZDA_COMM_CONFIG=&COMMPARM
ZDA_OTHER_CONFIG=&OTHPARM
PATH=&JAVAHOME./bin:/bin
_BPXK_AUTOCVT=ON
_BPX_SPAWN_SCRIPT=YES
_BPX_SHAREAS=YES
